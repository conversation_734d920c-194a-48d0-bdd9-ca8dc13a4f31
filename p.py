import os
import json
from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                            QComboBox, QFileDialog, QMessageBox, QScrollArea, QWidget, 
                            QSpinBox, QGroupBox, QLineEdit, QTabWidget, QCheckBox, 
                            QGridLayout, QDoubleSpinBox)
from PyQt5.QtGui import QPainter, QPen, QPixmap, QColor, QImage, QFont, QBrush
from PyQt5.QtCore import Qt, QPoint, QRect, QSize

class RecoilPatternCanvas(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.points = []
        self.background_image = None
        self.image_scale = 1.0
        self.setMinimumSize(600, 600)
        self.setCursor(Qt.CrossCursor)
        self.point_size = 8
        self.selected_point = -1
        self.setMouseTracking(True)
        self.grid_size = 20
        self.show_grid = True
        self.zoom_level = 1.0
        
    def set_background_image(self, image_path):
        if not image_path:
            self.background_image = None
            self.update()
            return
            
        try:
            self.background_image = QImage(image_path)
            if self.background_image.isNull():
                self.background_image = None
                return False
            self.update()
            return True
        except Exception as e:
            print(f"Error loading image: {e}")
            self.background_image = None
            return False
    
    def set_zoom(self, zoom):
        self.zoom_level = zoom
        self.update()
    
    def clear_points(self):
        self.points = []
        self.update()
    
    def set_points(self, points):
        self.points = points.copy()
        self.update()
    
    def get_points(self):
        return self.points.copy()
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            pos = event.pos()
            scaled_pos = QPoint(int(pos.x() / self.zoom_level), int(pos.y() / self.zoom_level))
            
            # Check if clicking on existing point
            for i, point in enumerate(self.points):
                point_rect = QRect(
                    int(point.x() * self.zoom_level) - self.point_size//2,
                    int(point.y() * self.zoom_level) - self.point_size//2,
                    self.point_size,
                    self.point_size
                )
                if point_rect.contains(pos):
                    self.selected_point = i
                    return
            
            # Add new point
            self.points.append(scaled_pos)
            self.update()
        elif event.button() == Qt.RightButton:
            # Remove point if right-clicking on it
            pos = event.pos()
            for i, point in enumerate(self.points):
                point_rect = QRect(
                    int(point.x() * self.zoom_level) - self.point_size//2,
                    int(point.y() * self.zoom_level) - self.point_size//2,
                    self.point_size,
                    self.point_size
                )
                if point_rect.contains(pos):
                    self.points.pop(i)
                    self.update()
                    return
    
    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.LeftButton and self.selected_point >= 0:
            pos = event.pos()
            scaled_pos = QPoint(int(pos.x() / self.zoom_level), int(pos.y() / self.zoom_level))
            self.points[self.selected_point] = scaled_pos
            self.update()
    
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.selected_point = -1
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Fill background
        painter.fillRect(self.rect(), QColor(30, 30, 30))
        
        # Draw grid if enabled
        if self.show_grid:
            painter.setPen(QPen(QColor(60, 60, 60), 1, Qt.SolidLine))
            
            # Calculate grid size based on zoom level
            grid_size = int(self.grid_size * self.zoom_level)
            if grid_size < 5:  # Minimum grid size for visibility
                grid_size = 5
            
            # Draw vertical grid lines
            for x in range(0, self.width(), grid_size):
                painter.drawLine(x, 0, x, self.height())
            
            # Draw horizontal grid lines
            for y in range(0, self.height(), grid_size):
                painter.drawLine(0, y, self.width(), y)
        
        # Draw background image if available
        if self.background_image and not self.background_image.isNull():
            scaled_width = int(self.background_image.width() * self.zoom_level)
            scaled_height = int(self.background_image.height() * self.zoom_level)
            
            # Center the image if it's smaller than the widget
            x = max(0, (self.width() - scaled_width) // 2)
            y = max(0, (self.height() - scaled_height) // 2)
            
            painter.drawImage(
                QRect(x, y, scaled_width, scaled_height),
                self.background_image,
                self.background_image.rect()
            )
        
        # Draw connecting lines between points
        if len(self.points) > 1:
            painter.setPen(QPen(QColor(0, 200, 255), 2, Qt.SolidLine))
            for i in range(len(self.points) - 1):
                p1 = QPoint(int(self.points[i].x() * self.zoom_level), 
                           int(self.points[i].y() * self.zoom_level))
                p2 = QPoint(int(self.points[i+1].x() * self.zoom_level), 
                           int(self.points[i+1].y() * self.zoom_level))
                painter.drawLine(p1, p2)
        
        # Draw points
        for i, point in enumerate(self.points):
            # Use different colors for first, last and other points
            if i == 0:
                painter.setBrush(QBrush(QColor(0, 255, 0)))  # Green for first point
            elif i == len(self.points) - 1:
                painter.setBrush(QBrush(QColor(255, 0, 0)))  # Red for last point
            else:
                painter.setBrush(QBrush(QColor(255, 255, 0)))  # Yellow for middle points
            
            # Draw point
            scaled_x = int(point.x() * self.zoom_level)
            scaled_y = int(point.y() * self.zoom_level)
            painter.setPen(QPen(Qt.black, 1))
            painter.drawEllipse(scaled_x - self.point_size//2, scaled_y - self.point_size//2, 
                              self.point_size, self.point_size)
            
            # Draw point number
            painter.setPen(QPen(Qt.white, 1))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(scaled_x + self.point_size//2 + 2, scaled_y + self.point_size//2, str(i))

class AttachmentTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.canvas = None
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Attachment selection
        attachment_group = QGroupBox("Attachment Configuration")
        attachment_layout = QGridLayout(attachment_group)
        
        # Muzzle selection
        attachment_layout.addWidget(QLabel("Muzzle:"), 0, 0)
        self.muzzle_combo = QComboBox()
        self.muzzle_combo.addItems([
            "None", "Compensator", "Flash Hider", "Suppressor"
        ])
        self.muzzle_combo.currentIndexChanged.connect(self.on_attachment_changed)
        attachment_layout.addWidget(self.muzzle_combo, 0, 1)
        
        # Grip selection
        attachment_layout.addWidget(QLabel("Grip:"), 1, 0)
        self.grip_combo = QComboBox()
        self.grip_combo.addItems([
            "None", "Vertical Grip", "Half Grip", "Thumb Grip", "Angled Grip"
        ])
        self.grip_combo.currentIndexChanged.connect(self.on_attachment_changed)
        attachment_layout.addWidget(self.grip_combo, 1, 1)
        
        # Stock selection
        attachment_layout.addWidget(QLabel("Stock:"), 2, 0)
        self.stock_combo = QComboBox()
        self.stock_combo.addItems([
            "None", "Tactical Stock", "Cheek Pad"
        ])
        self.stock_combo.currentIndexChanged.connect(self.on_attachment_changed)
        attachment_layout.addWidget(self.stock_combo, 2, 1)
        
        layout.addWidget(attachment_group)
        
        # Canvas container
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        self.canvas = RecoilPatternCanvas()
        scroll_area.setWidget(self.canvas)
        
        layout.addWidget(scroll_area)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("Clear Points")
        self.clear_btn.clicked.connect(self.clear_points)
        controls_layout.addWidget(self.clear_btn)
        
        self.point_count_label = QLabel("Points: 0")
        controls_layout.addWidget(self.point_count_label)
        
        layout.addLayout(controls_layout)
    
    def on_attachment_changed(self):
        # Signal to parent that attachment configuration has changed
        parent = self.parent()
        while parent and not isinstance(parent, RecoilPatternEditor):
            parent = parent.parent()
            
        if parent:
            parent.update_tab_title(self)
    
    def clear_points(self):
        reply = QMessageBox.question(
            self, "Confirm", "Are you sure you want to clear all points?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.canvas.clear_points()
            self.update_point_count()
    
    def update_point_count(self):
        count = len(self.canvas.get_points())
        self.point_count_label.setText(f"Points: {count}")
    
    def get_attachment_code(self):
        # Convert attachment selections to code format like A0B0C0
        muzzle_index = self.muzzle_combo.currentIndex()
        grip_index = self.grip_combo.currentIndex()
        stock_index = self.stock_combo.currentIndex()
        
        return f"A{muzzle_index}B{grip_index}C{stock_index}"
    
    def set_attachment_code(self, code):
        # Parse code like A0B0C0
        try:
            muzzle = int(code[1])
            grip = int(code[3])
            stock = int(code[5])
            
            if 0 <= muzzle < self.muzzle_combo.count():
                self.muzzle_combo.setCurrentIndex(muzzle)
            if 0 <= grip < self.grip_combo.count():
                self.grip_combo.setCurrentIndex(grip)
            if 0 <= stock < self.stock_combo.count():
                self.stock_combo.setCurrentIndex(stock)
        except:
            pass
    
    def get_points(self):
        return self.canvas.get_points()
    
    def set_points(self, points):
        self.canvas.set_points(points)
        self.update_point_count()
    
    def set_background_image(self, image_path):
        return self.canvas.set_background_image(image_path)
    
    def set_zoom(self, zoom):
        self.canvas.set_zoom(zoom)

class RecoilPatternEditor(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Recoil Pattern Editor")
        self.setMinimumSize(900, 700)
        self.attachment_tabs = {}
        self.current_image = None
        self.setup_ui()
        self.load_weapons()
    
    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        
        # Top controls
        top_controls = QHBoxLayout()
        
        # Weapon selection
        weapon_group = QGroupBox("Weapon")
        weapon_layout = QVBoxLayout(weapon_group)
        
        self.weapon_combo = QComboBox()
        self.weapon_combo.setMinimumWidth(150)
        self.weapon_combo.currentIndexChanged.connect(self.on_weapon_changed)
        weapon_layout.addWidget(self.weapon_combo)
        
        self.new_weapon_input = QLineEdit()
        self.new_weapon_input.setPlaceholderText("New weapon name")
        weapon_layout.addWidget(self.new_weapon_input)
        
        self.add_weapon_btn = QPushButton("Add Weapon")
        self.add_weapon_btn.clicked.connect(self.add_new_weapon)
        weapon_layout.addWidget(self.add_weapon_btn)
        
        top_controls.addWidget(weapon_group)
        
        # Posture multipliers
        posture_group = QGroupBox("Posture Multipliers")
        posture_layout = QGridLayout(posture_group)
        
        posture_layout.addWidget(QLabel("Standing:"), 0, 0)
        self.standing_spin = QDoubleSpinBox()
        self.standing_spin.setRange(0.1, 2.0)
        self.standing_spin.setValue(1.0)
        self.standing_spin.setSingleStep(0.05)
        posture_layout.addWidget(self.standing_spin, 0, 1)
        
        posture_layout.addWidget(QLabel("Crouching:"), 1, 0)
        self.crouch_spin = QDoubleSpinBox()
        self.crouch_spin.setRange(0.1, 2.0)
        self.crouch_spin.setValue(0.80)
        self.crouch_spin.setSingleStep(0.05)
        posture_layout.addWidget(self.crouch_spin, 1, 1)
        
        posture_layout.addWidget(QLabel("Prone:"), 2, 0)
        self.prone_spin = QDoubleSpinBox()
        self.prone_spin.setRange(0.1, 2.0)
        self.prone_spin.setValue(0.55)
        self.prone_spin.setSingleStep(0.05)
        posture_layout.addWidget(self.prone_spin, 2, 1)
        
        top_controls.addWidget(posture_group)
        
        # Image controls
        image_group = QGroupBox("Reference Image")
        image_layout = QVBoxLayout(image_group)
        
        self.load_image_btn = QPushButton("Load Image")
        self.load_image_btn.clicked.connect(self.load_image)
        image_layout.addWidget(self.load_image_btn)
        
        self.clear_image_btn = QPushButton("Clear Image")
        self.clear_image_btn.clicked.connect(self.clear_image)
        image_layout.addWidget(self.clear_image_btn)
        
        top_controls.addWidget(image_group)
        
        # Zoom controls
        zoom_group = QGroupBox("Zoom")
        zoom_layout = QVBoxLayout(zoom_group)
        
        zoom_controls = QHBoxLayout()
        zoom_label = QLabel("Zoom Level:")
        self.zoom_spin = QSpinBox()
        self.zoom_spin.setRange(10, 500)
        self.zoom_spin.setValue(100)
        self.zoom_spin.setSuffix("%")
        self.zoom_spin.setSingleStep(10)
        self.zoom_spin.valueChanged.connect(self.update_zoom)
        
        zoom_controls.addWidget(zoom_label)
        zoom_controls.addWidget(self.zoom_spin)
        zoom_layout.addLayout(zoom_controls)
        
        top_controls.addWidget(zoom_group)
        
        main_layout.addLayout(top_controls)
        
        # Tabs for different attachment combinations
        self.tab_widget = QTabWidget()
        
        # Create default tab
        default_tab = AttachmentTab()
        self.tab_widget.addTab(default_tab, "Default (A0B0C0)")
        self.attachment_tabs["A0B0C0"] = default_tab
        
        # Add button to add new attachment configuration
        self.add_attachment_btn = QPushButton("Add Attachment Configuration")
        self.add_attachment_btn.clicked.connect(self.add_attachment_tab)
        main_layout.addWidget(self.add_attachment_btn)
        
        main_layout.addWidget(self.tab_widget)
        
        # Instructions
        instructions = QLabel(
            "Left click to add points. Right click to remove points. "
            "Drag existing points to move them. "
            "Points will be connected in the order they are added. "
            "Create different patterns for different attachment combinations."
        )
        instructions.setWordWrap(True)
        main_layout.addWidget(instructions)
        
        # Bottom buttons
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("Save Pattern")
        self.save_btn.clicked.connect(self.save_pattern)
        buttons_layout.addWidget(self.save_btn)
        
        self.load_btn = QPushButton("Load Pattern")
        self.load_btn.clicked.connect(self.load_pattern)
        buttons_layout.addWidget(self.load_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def update_tab_title(self, tab):
        # Find the tab index
        for i in range(self.tab_widget.count()):
            if self.tab_widget.widget(i) == tab:
                # Get the new attachment code
                new_code = tab.get_attachment_code()
                
                # Update the tab title
                self.tab_widget.setTabText(i, f"Config ({new_code})")
                
                # Update the attachment_tabs dictionary
                # First, find the old key
                old_key = None
                for key, value in self.attachment_tabs.items():
                    if value == tab:
                        old_key = key
                        break
                
                if old_key and old_key != new_code:
                    # Remove the old key and add with the new key
                    self.attachment_tabs[new_code] = tab
                    if old_key != new_code:
                        del self.attachment_tabs[old_key]
                break
    
    def add_attachment_tab(self):
        # Get current tab's attachment configuration
        current_tab = self.tab_widget.currentWidget()
        if isinstance(current_tab, AttachmentTab):
            # Create a new tab
            new_tab = AttachmentTab()
            
            # Add the tab with a temporary code
            temp_code = self.get_unique_attachment_code()
            self.tab_widget.addTab(new_tab, f"Config ({temp_code})")
            self.attachment_tabs[temp_code] = new_tab
            
            # Set same image and zoom
            if self.current_image:
                new_tab.set_background_image(self.current_image)
            new_tab.set_zoom(self.zoom_spin.value() / 100.0)
            
            # Switch to the new tab
            self.tab_widget.setCurrentWidget(new_tab)
            
            # Update the tab title based on its actual configuration
            self.update_tab_title(new_tab)
    
    def get_unique_attachment_code(self):
        # Find a unique attachment code not already in use
        for muzzle in range(10):
            for grip in range(5):
                for stock in range(3):
                    code = f"A{muzzle}B{grip}C{stock}"
                    if code not in self.attachment_tabs:
                        return code
        
        # If all codes are used (unlikely), generate a random one
        import random
        while True:
            code = f"A{random.randint(0,9)}B{random.randint(0,4)}C{random.randint(0,2)}"
            if code not in self.attachment_tabs:
                return code
    
    def load_weapons(self):
        try:
            # Load existing weapon patterns
            pattern_file = './Config/weapon_patterns.json'
            if os.path.exists(pattern_file):
                with open(pattern_file, 'r', encoding='utf-8') as f:
                    patterns = json.load(f)
                    weapons = sorted(patterns.keys())
                    self.weapon_combo.addItems(weapons)
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load weapons: {str(e)}")
    
    def on_weapon_changed(self, index):
        if index >= 0:
            weapon = self.weapon_combo.currentText()
            self.load_pattern(weapon)
    
    def add_new_weapon(self):
        weapon_name = self.new_weapon_input.text().strip()
        if not weapon_name:
            QMessageBox.warning(self, "Error", "Please enter a weapon name")
            return
            
        # Check if weapon already exists
        for i in range(self.weapon_combo.count()):
            if self.weapon_combo.itemText(i).lower() == weapon_name.lower():
                QMessageBox.warning(self, "Error", f"Weapon '{weapon_name}' already exists")
                return
                
        self.weapon_combo.addItem(weapon_name)
        self.weapon_combo.setCurrentText(weapon_name)
        self.new_weapon_input.clear()
    
    def load_image(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Reference Image", "", "Images (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.current_image = file_path
            
            # Apply to all tabs
            for tab in self.attachment_tabs.values():
                success = tab.set_background_image(file_path)
            
            if not success:
                QMessageBox.warning(self, "Error", "Failed to load image")
    
    def clear_image(self):
        self.current_image = None
        for tab in self.attachment_tabs.values():
            tab.set_background_image(None)
    
    def update_zoom(self, value):
        zoom = value / 100.0
        for tab in self.attachment_tabs.values():
            tab.set_zoom(zoom)
    
    def save_pattern(self):
        weapon = self.weapon_combo.currentText()
        if not weapon:
            QMessageBox.warning(self, "Error", "Please select or add a weapon")
            return
            
        # Check if at least one tab has points
        has_points = False
        for tab in self.attachment_tabs.values():
            if len(tab.get_points()) > 0:
                has_points = True
                break
                
        if not has_points:
            QMessageBox.warning(self, "Error", "No points to save in any tab")
            return
            
        try:
            # Load existing patterns
            pattern_file = './Config/weapon_patterns.json'
            patterns = {}
            if os.path.exists(pattern_file):
                with open(pattern_file, 'r', encoding='utf-8') as f:
                    patterns = json.load(f)
            
            # Create custom patterns directory if it doesn't exist
            custom_dir = './_internal/GunData'
            os.makedirs(custom_dir, exist_ok=True)
            
            # Save detailed pattern to custom file
            custom_file = f"{custom_dir}/{weapon.lower()}.json"
            
            # Create basic structure for the custom pattern file
            custom_data = {
                "none": self.standing_spin.value(),
                "c": self.crouch_spin.value(),
                "z": self.prone_spin.value()
            }
            
            # Add patterns for each attachment configuration
            total_points = 0
            for tab in self.attachment_tabs.values():
                points = tab.get_points()
                if points:
                    # Get the current attachment code from the tab
                    code = tab.get_attachment_code()
                    
                    # Convert points to recoil pattern format
                    pattern = [5]  # Start with initial delay
                    
                    prev_x, prev_y = points[0].x(), points[0].y()
                    for point in points[1:]:
                        # Calculate relative movement from previous point
                        dx = point.x() - prev_x
                        dy = point.y() - prev_y
                        
                        # Add to pattern: [0, dx, dy]
                        # 0 is the delay between shots (can be adjusted if needed)
                        pattern.extend([0, int(dx), int(dy)])
                        
                        # Update previous point
                        prev_x, prev_y = point.x(), point.y()
                    
                    custom_data[code] = pattern
                    total_points += len(points)
                else:
                    # Get the current attachment code from the tab
                    code = tab.get_attachment_code()
                    custom_data[code] = [0, 0, 0]
            
            # Save the custom pattern file
            with open(custom_file, 'w', encoding='utf-8') as f:
                json.dump(custom_data, f, indent=4)
            
            # Update the main patterns file with the recoil value
            # Use the point count as a simple recoil value or let user define it
            patterns[weapon.lower()] = float(total_points / 10)  # Simple heuristic
            
            with open(pattern_file, 'w', encoding='utf-8') as f:
                json.dump(patterns, f, indent=4, ensure_ascii=False)
            
            QMessageBox.information(
                self, "Success", 
                f"Pattern saved for {weapon} with {total_points} total points across {len(self.attachment_tabs)} configurations"
            )
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save pattern: {str(e)}")
    
    def load_pattern(self, weapon_name=None):
        if weapon_name is None:
            weapon_name = self.weapon_combo.currentText()
            
        if not weapon_name:
            QMessageBox.warning(self, "Error", "Please select a weapon")
            return
            
        try:
            # Try to load custom pattern
            custom_file = f"./_internal/GunData/{weapon_name.lower()}.json"
            if os.path.exists(custom_file):
                with open(custom_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # Load posture multipliers
                    if "none" in data:
                        self.standing_spin.setValue(float(data["none"]))
                    if "c" in data:
                        self.crouch_spin.setValue(float(data["c"]))
                    if "z" in data:
                        self.prone_spin.setValue(float(data["z"]))
                    
                    # Clear existing tabs except default
                    for i in range(self.tab_widget.count() - 1, 0, -1):
                        self.tab_widget.removeTab(i)
                    
                    # Keep only the default tab
                    default_tab = self.attachment_tabs["A0B0C0"]
                    self.attachment_tabs = {"A0B0C0": default_tab}
                    
                    # Load patterns for each attachment configuration
                    for code, pattern in data.items():
                        # Skip non-pattern keys
                        if code in ["none", "c", "z"]:
                            continue
                            
                        # Create tab if it doesn't exist
                        if code not in self.attachment_tabs:
                            new_tab = AttachmentTab()
                            self.tab_widget.addTab(new_tab, f"Config ({code})")
                            self.attachment_tabs[code] = new_tab
                            
                            # Set the attachment configuration based on the code
                            new_tab.set_attachment_code(code)
                            
                            # Set same image and zoom as default tab
                            if self.current_image:
                                new_tab.set_background_image(self.current_image)
                            new_tab.set_zoom(self.zoom_spin.value() / 100.0)
                        
                        # Skip empty patterns
                        if not pattern or len(pattern) < 4:
                            continue
                        
                        # Convert pattern to points
                        points = []
                        x, y = 100, 100  # Starting position
                        
                        # Skip the first value (initial delay)
                        i = 1
                        while i < len(pattern):
                            if i + 2 < len(pattern):
                                # Extract delay, dx, dy
                                delay = pattern[i]
                                dx = pattern[i+1]
                                dy = pattern[i+2]
                                
                                # Add point
                                if not points:
                                    points.append(QPoint(x, y))
                                
                                # Update position and add new point
                                x += dx
                                y += dy
                                points.append(QPoint(x, y))
                                
                                i += 3
                            else:
                                break
                        
                        if points:
                            self.attachment_tabs[code].set_points(points)
                            
                    # Update all tab titles to ensure they match the current attachment configuration
                    for tab in list(self.attachment_tabs.values()):
                        self.update_tab_title(tab)
                    
                    QMessageBox.information(
                        self, "Success", 
                        f"Loaded pattern for {weapon_name} with {len(self.attachment_tabs)} configurations"
                    )
                    return
                    
            QMessageBox.information(
                self, "Not Found", 
                f"No custom pattern found for {weapon_name}. Creating new pattern."
            )
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load pattern: {str(e)}") 