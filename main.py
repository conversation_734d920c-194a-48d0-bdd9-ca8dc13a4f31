import sys
import fire_data
import Process
import macro
from PyQt5.QtCore import <PERSON><PERSON><PERSON><PERSON>, Qt, pyqtSignal, QEvent
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QMessageBox, QDialog, Q<PERSON>ush<PERSON>utton
from PyQt5 import QtWidgets, QtGui
from PUBG_UI import Ui_PUBG
from MouseListener import App<PERSON>ain<PERSON>ouseListener
from KeyListener import App<PERSON>ain<PERSON><PERSON><PERSON>istener
from weapon_editor import WeaponEditor
from crosshair import Crosshair
import os
from scope_editor import ScopeEditor
import json

class AppManager(QMainWindow, Ui_PUBG):
    def __init__(self):
        super().__init__()
        self.isHidden = None
        self.my_mouse_thread = None
        self.my_key_thread = None
        self.pauses = True
        self.TextValue = {'无': 'none', '红点': 'hongdian', '全息': "quanxi", 'Нет': 'none'}
        self.crosshair = None
        
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        self.init_ui()

    def init_ui(self):
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.setupUi(self)
        
        background_image = os.path.join(os.path.dirname(__file__), 'img.jpg')
        if os.path.exists(background_image):
            self.setStyleSheet(f"""
                QWidget#centralwidget {{
                    background-image: url({background_image});
                    background-position: center;
                    background-repeat: no-repeat;
                }}
                QLabel {{
                    color: white;
                }}
                QPushButton {{
                    background-color: rgba(40, 40, 40, 180);
                    color: white;
                    border: 1px solid #555;
                    border-radius: 3px;
                    padding: 5px;
                }}
                QPushButton:hover {{
                    background-color: rgba(60, 60, 60, 180);
                }}
                QComboBox {{
                    background-color: rgba(40, 40, 40, 180);
                    color: white;
                    border: 1px solid #555;
                }}
                QGroupBox {{
                    background-color: rgba(20, 20, 20, 150);
                    color: white;
                    border: 1px solid #555;
                    border-radius: 5px;
                    margin-top: 10px;
                    padding-top: 10px;
                }}
                QTextEdit {{
                    background-color: rgba(20, 20, 20, 150);
                    color: white;
                    border: 1px solid #555;
                }}
            """)
        
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        
        self.title_font = QtGui.QFont()
        self.title_font.setPointSize(14)
        self.title_font.setBold(True)
        
        self.button_font = QtGui.QFont()
        self.button_font.setPointSize(12)
        self.button_font.setBold(True)
        
        self.EditPatternsBtn = QtWidgets.QPushButton("Edit Recoil")
        self.EditPatternsBtn.setMinimumHeight(30)
        self.EditPatternsBtn.setFont(self.button_font)
        self.EditPatternsBtn.clicked.connect(self.open_weapon_editor)

        self.EditScopesBtn = QtWidgets.QPushButton("Edit Scopes")
        self.EditScopesBtn.setMinimumHeight(30)
        self.EditScopesBtn.setFont(self.button_font)
        self.EditScopesBtn.clicked.connect(self.open_scope_editor)

        self.create_buttons()
        
        main_layout = QtWidgets.QVBoxLayout(self.central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        self.create_layout_containers(main_layout)
        
        self.setMinimumSize(600, 800)
        self.resize(600, 800)
        
        self.init_components()
        
        self.setMinimumSize(700, 900)
        self.resize(700, 900)

        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a1a1a;
            }
            QWidget {
                color: #b388ff;
                font-family: 'Segoe UI', Arial;
                font-size: 12px;
                spacing: 15px;
            }
            QGroupBox {
                background-color: #242424;
                border: 2px solid #4a148c;
                border-radius: 12px;
                margin-top: 15px;
                padding: 20px;
                font-weight: bold;
            }
            QGroupBox::title {
                color: #e1bee7;
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                font-size: 14px;
            }
            QPushButton {
                background-color: #4a148c;
                border: none;
                border-radius: 8px;
                color: white;
                padding: 12px 25px;
                font-weight: bold;
                min-height: 35px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #6a1b9a;
            }
            QPushButton:pressed {
                background-color: #38006b;
                padding: 14px 25px 10px 25px;
            }
            QPushButton:checked {
                background-color: #7b1fa2;
                border: 2px solid #ce93d8;
            }
            QComboBox {
                background-color: #242424;
                border: 2px solid #4a148c;
                border-radius: 8px;
                padding: 8px;
                color: #b388ff;
                min-height: 30px;
                margin: 5px;
            }
            QComboBox:hover {
                border-color: #7b1fa2;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QSpinBox, QDoubleSpinBox {
                background-color: #242424;
                border: 2px solid #4a148c;
                border-radius: 8px;
                padding: 8px;
                color: #b388ff;
                min-height: 30px;
                margin: 5px;
            }
            QSpinBox:hover, QDoubleSpinBox:hover {
                border-color: #7b1fa2;
            }
            QLabel {
                color: #e1bee7;
                font-size: 13px;
                padding: 5px;
            }
            QTextEdit {
                background-color: #242424;
                border: 2px solid #4a148c;
                border-radius: 8px;
                color: #b388ff;
                padding: 10px;
                margin: 5px;
            }
            QCheckBox {
                spacing: 10px;
                color: #e1bee7;
            }
            QCheckBox::indicator {
                width: 22px;
                height: 22px;
                border: 2px solid #4a148c;
                border-radius: 6px;
                background-color: #242424;
            }
            QCheckBox::indicator:checked {
                background-color: #7b1fa2;
                border: 2px solid #ce93d8;
            }
            QRadioButton {
                color: #e1bee7;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 22px;
                height: 22px;
                border: 2px solid #4a148c;
                border-radius: 11px;
                background-color: #242424;
            }
            QRadioButton::indicator:checked {
                background-color: #7b1fa2;
                border: 2px solid #ce93d8;
            }
            QScrollBar:vertical {
                border: none;
                background-color: #242424;
                width: 14px;
                margin: 0px;
                border-radius: 7px;
            }
            QScrollBar::handle:vertical {
                background-color: #4a148c;
                border-radius: 7px;
                min-height: 30px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #6a1b9a;
            }
            QStatusBar {
                background-color: #242424;
                color: #e1bee7;
            }
            QLineEdit {
                background-color: #242424;
                border: 2px solid #4a148c;
                border-radius: 8px;
                padding: 8px;
                color: #b388ff;
                margin: 5px;
            }
            
            QVBoxLayout, QHBoxLayout {
                spacing: 5px;
                margin: 10px;
            }
            
            #TitleBox {
                font-size: 18px;
                font-weight: bold;
                color: #e1bee7;
                padding: 15px;
            }
        """)

        self.setMinimumSize(1000, 1000)
        self.resize(1000, 1000)

        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a1a1a;
            }
            QWidget {
                color: #b388ff;
                font-family: 'Segoe UI', Arial;
                font-size: 11px;
                spacing: 8px;
            }
            QGroupBox {
                background-color: #242424;
                border: 1px solid #4a148c;
                border-radius: 8px;
                margin-top: 10px;
                padding: 12px;
                font-weight: bold;
            }
            QGroupBox::title {
                color: #e1bee7;
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 5px;
                font-size: 12px;
            }
            QPushButton {
                background-color: #4a148c;
                border: none;
                border-radius: 4px;
                color: white;
                padding: 6px 15px;
                font-weight: bold;
                min-height: 25px;
                margin: 3px;
            }
            QComboBox {
                background-color: #242424;
                border: 1px solid #4a148c;
                border-radius: 4px;
                padding: 4px;
                color: #b388ff;
                min-height: 20px;
                margin: 3px;
            }
            QSpinBox, QDoubleSpinBox {
                background-color: #242424;
                border: 1px solid #4a148c;
                border-radius: 4px;
                padding: 4px;
                color: #b388ff;
                min-height: 20px;
                margin: 3px;
            }
            QLabel {
                color: #e1bee7;
                font-size: 11px;
                padding: 2px;
            }
            QTextEdit {
                background-color: #242424;
                border: 1px solid #4a148c;
                border-radius: 4px;
                color: #b388ff;
                padding: 5px;
                margin: 3px;
            }
            QCheckBox {
                spacing: 5px;
                color: #e1bee7;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #4a148c;
                border-radius: 3px;
            }
            QRadioButton {
                color: #e1bee7;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #4a148c;
                border-radius: 8px;
            }
            
            #TitleBox {
                font-size: 14px;
                font-weight: bold;
                color: #e1bee7;
                padding: 10px;
            }
        """)

    def create_buttons(self):
        self.OverlayBtn = QtWidgets.QPushButton("Enable Overlay")
        self.OverlayBtn.setCheckable(True)
        self.OverlayBtn.setChecked(PC.overlay_enabled)
        self.OverlayBtn.clicked.connect(self.toggle_overlay)
        self.OverlayBtn.setFont(self.button_font)
        
        self.CrosshairBtn = QtWidgets.QPushButton("Enable Crosshair")
        self.CrosshairBtn.setCheckable(True)
        self.CrosshairBtn.setChecked(PC.crosshair_enabled)
        self.CrosshairBtn.clicked.connect(self.toggle_crosshair)
        self.CrosshairBtn.setFont(self.button_font)

    def create_layout_containers(self, main_layout):
        header_panel = QtWidgets.QVBoxLayout()
        header_panel.addWidget(self.TitleBox)
        main_layout.addLayout(header_panel)
        
        resolution_panel = QtWidgets.QHBoxLayout()
        resolution_panel.addWidget(self.ResolutionSelect)
        resolution_panel.addWidget(self.ResolutionBtn)
        main_layout.addLayout(resolution_panel)
        
        control_panel = QtWidgets.QHBoxLayout()
        control_panel.addWidget(self.Startbtn)
        control_panel.addWidget(self.Pausebtn)
        control_panel.addWidget(self.Stopbtn)
        main_layout.addLayout(control_panel)
        
        main_layout.addWidget(self.StatusInfo)
        
        main_layout.addWidget(self.ListenModeBox)
        
        crosshair_group = QtWidgets.QGroupBox("Crosshair Settings")
        crosshair_layout = QtWidgets.QVBoxLayout(crosshair_group)
        
        crosshair_layout.addWidget(self.CrosshairBtn)
        
        resolution_layout = QtWidgets.QHBoxLayout()
        resolution_label = QtWidgets.QLabel("Screen Resolution:")
        self.CrosshairResolutionCombo = QtWidgets.QComboBox()
        self.CrosshairResolutionCombo.addItems([
            "1920x1080",
            "2560x1440", 
            "3440x1440",
            "2560x1080",
            "1728x1080",
            "3840x2160"
        ])
        resolution_saved = PC.get_config_data('crosshair_resolution') or "1920x1080"
        self.CrosshairResolutionCombo.setCurrentText(resolution_saved)
        self.CrosshairResolutionCombo.currentTextChanged.connect(self.on_crosshair_resolution_changed)
        resolution_layout.addWidget(resolution_label)
        resolution_layout.addWidget(self.CrosshairResolutionCombo)
        crosshair_layout.addLayout(resolution_layout)
        
        crosshair_settings = QtWidgets.QHBoxLayout()
        
        type_layout = QtWidgets.QHBoxLayout()
        type_layout.addWidget(QtWidgets.QLabel("Type:"))
        self.CrosshairTypeCombo = QtWidgets.QComboBox()
        self.CrosshairTypeCombo.addItems(["Simple"])
        self.CrosshairTypeCombo.currentTextChanged.connect(self.on_crosshair_type_changed)
        type_layout.addWidget(self.CrosshairTypeCombo)
        crosshair_settings.addLayout(type_layout)
        
        color_layout = QtWidgets.QHBoxLayout()
        color_layout.addWidget(QtWidgets.QLabel("Color:"))
        self.CrosshairColorCombo = QtWidgets.QComboBox()
        self.CrosshairColorCombo.addItems(["White", "Red", "Green", "Blue"])
        self.CrosshairColorCombo.currentTextChanged.connect(self.on_crosshair_color_changed)
        color_layout.addWidget(self.CrosshairColorCombo)
        crosshair_settings.addLayout(color_layout)
        
        size_layout = QtWidgets.QHBoxLayout()
        size_layout.addWidget(QtWidgets.QLabel("Size:"))
        self.CrosshairSizeSpinner = QtWidgets.QDoubleSpinBox()
        self.CrosshairSizeSpinner.setRange(0.5, 3.0)
        self.CrosshairSizeSpinner.setSingleStep(0.1)
        self.CrosshairSizeSpinner.setValue(1.0)
        self.CrosshairSizeSpinner.valueChanged.connect(self.on_crosshair_size_changed)
        size_layout.addWidget(self.CrosshairSizeSpinner)
        crosshair_settings.addLayout(size_layout)
        
        crosshair_layout.addLayout(crosshair_settings)
        main_layout.addWidget(crosshair_group)
        
        crouch_group = QtWidgets.QGroupBox("Crouch Settings")
        crouch_layout = QtWidgets.QVBoxLayout(crouch_group)
        
        crouch_controls = QtWidgets.QHBoxLayout()
        
        crouch_label = QtWidgets.QLabel("Key:")
        crouch_controls.addWidget(crouch_label)
        
        self.CrouchKeyCombo = QtWidgets.QComboBox()
        self.CrouchKeyCombo.addItems(["CTRL", "ALT", "C"])
        self.CrouchKeyCombo.setCurrentText("CTRL")
        crouch_controls.addWidget(self.CrouchKeyCombo)
        
        self.CrouchKeyBtn = QtWidgets.QPushButton("Save")
        self.CrouchKeyBtn.clicked.connect(self.save_crouch_key)
        crouch_controls.addWidget(self.CrouchKeyBtn)
        
        crouch_layout.addLayout(crouch_controls)
        
        mode_layout = QtWidgets.QHBoxLayout()
        self.CrouchToggleCheck = QtWidgets.QCheckBox("Toggle Mode")
        self.CrouchToggleCheck.setChecked(PC.crouch_toggle_mode)
        self.CrouchToggleCheck.stateChanged.connect(self.on_crouch_toggle_changed)
        mode_layout.addWidget(self.CrouchToggleCheck)
        
        crouch_layout.addLayout(mode_layout)
        crouch_layout.addLayout(crouch_controls)
        main_layout.addWidget(crouch_group)
        
        sensitivity_group = QtWidgets.QGroupBox("Sensitivity Settings")
        sensitivity_group.setMinimumHeight(120)
        sensitivity_layout = QtWidgets.QVBoxLayout(sensitivity_group)
        sensitivity_layout.setSpacing(10)

        
        ads_layout = QtWidgets.QHBoxLayout()
        ads_layout.setSpacing(10)
        ads_label = QtWidgets.QLabel("ADS Sensitivity:")
        ads_label.setMinimumWidth(150)
        self.ADSSpinBox = QtWidgets.QSpinBox()
        self.ADSSpinBox.setMinimumWidth(100)
        self.ADSSpinBox.setRange(1, 100)
        self.ADSSpinBox.setValue(36)
        self.ADSSpinBox.setSingleStep(1)
        self.ADSSpinBox.valueChanged.connect(self.on_ads_changed)
        ads_layout.addWidget(ads_label)
        ads_layout.addWidget(self.ADSSpinBox)
        sensitivity_layout.addLayout(ads_layout)
        
        vertical_layout = QtWidgets.QHBoxLayout()
        vertical_layout.setSpacing(10)
        vertical_label = QtWidgets.QLabel("Vertical Multiplier:")
        vertical_label.setMinimumWidth(150)
        self.VerticalSpinBox = QtWidgets.QDoubleSpinBox()
        self.VerticalSpinBox.setMinimumWidth(100)
        self.VerticalSpinBox.setRange(0.1, 2.0)
        self.VerticalSpinBox.setValue(1.45)
        self.VerticalSpinBox.setSingleStep(0.1)
        self.VerticalSpinBox.valueChanged.connect(self.on_vertical_changed)
        vertical_layout.addWidget(vertical_label)
        vertical_layout.addWidget(self.VerticalSpinBox)
        sensitivity_layout.addLayout(vertical_layout)
        
        scope_layout = QtWidgets.QHBoxLayout()
        scope_layout.setSpacing(10)
        scope_label = QtWidgets.QLabel("Scope Multiplier:")
        scope_label.setMinimumWidth(150)
        self.ScopeSpinBox = QtWidgets.QSpinBox()
        self.ScopeSpinBox.setMinimumWidth(100)
        self.ScopeSpinBox.setRange(1, 100)
        self.ScopeSpinBox.setValue(35)
        self.ScopeSpinBox.setSingleStep(1)
        self.ScopeSpinBox.valueChanged.connect(self.on_scope_changed)
        scope_layout.addWidget(scope_label)
        scope_layout.addWidget(self.ScopeSpinBox)
        sensitivity_layout.addLayout(scope_layout)
        
        main_layout.addWidget(sensitivity_group)
        
        tools_panel = QtWidgets.QHBoxLayout()
        tools_panel.addWidget(self.EditPatternsBtn)
        tools_panel.addWidget(self.OverlayBtn)

        
        overlay_controls = QtWidgets.QHBoxLayout()
        
        opacity_layout = QtWidgets.QHBoxLayout()
        opacity_layout.addWidget(QtWidgets.QLabel("Opacity:"))
        self.OpacitySpinBox = QtWidgets.QSpinBox()
        self.OpacitySpinBox.setRange(10, 100)
        self.OpacitySpinBox.setValue(PC.get_config_data('overlay_opacity') or 50)
        self.OpacitySpinBox.setSingleStep(5)
        self.OpacitySpinBox.setSuffix("%")
        self.OpacitySpinBox.valueChanged.connect(self.change_overlay_opacity)
        opacity_layout.addWidget(self.OpacitySpinBox)
        
        x_layout = QtWidgets.QHBoxLayout()
        x_layout.addWidget(QtWidgets.QLabel("X:"))
        self.OverlayXSpinBox = QtWidgets.QSpinBox()
        screen = QApplication.primaryScreen().geometry()
        self.OverlayXSpinBox.setRange(0, screen.width())
        self.OverlayXSpinBox.setValue(screen.width() - 220)
        self.OverlayXSpinBox.setSingleStep(10)
        self.OverlayXSpinBox.valueChanged.connect(self.change_overlay_position)
        x_layout.addWidget(self.OverlayXSpinBox)
        
        y_layout = QtWidgets.QHBoxLayout()
        y_layout.addWidget(QtWidgets.QLabel("Y:"))
        self.OverlayYSpinBox = QtWidgets.QSpinBox()
        self.OverlayYSpinBox.setRange(0, screen.height())
        self.OverlayYSpinBox.setValue(10)
        self.OverlayYSpinBox.setSingleStep(10)
        self.OverlayYSpinBox.valueChanged.connect(self.change_overlay_position)
        y_layout.addWidget(self.OverlayYSpinBox)
        
        overlay_controls.addWidget(self.OverlayBtn)
        overlay_controls.addLayout(opacity_layout)
        overlay_controls.addLayout(x_layout)
        overlay_controls.addLayout(y_layout)
        
        # Add reset position button
        self.ResetOverlayPosBtn = QtWidgets.QPushButton("Reset Position")
        self.ResetOverlayPosBtn.clicked.connect(self.reset_overlay_position)
        overlay_controls.addWidget(self.ResetOverlayPosBtn)
        
        tools_panel.addLayout(overlay_controls)
        
        main_layout.addLayout(tools_panel)
        
        main_layout.addWidget(self.IdentifyBox)
        main_layout.addWidget(self.Info)
        
        self.CrosshairTypeCombo.addItems(["Simple"])
        self.CrosshairColorCombo.addItems(["White", "Red", "Green", "Blue"])
        
        self.CrosshairTypeCombo.setCurrentText("Simple" if PC.crosshair_type == "simple" else "1")
        self.CrosshairColorCombo.setCurrentText(self.get_color_text(PC.crosshair_color))

        self.setMinimumSize(700, 1000)
        self.resize(700, 1000)

        for group in [crosshair_group, crouch_group, sensitivity_group]:
            group.setMinimumHeight(100)
        
        for layout in [crosshair_layout, crouch_layout, sensitivity_layout]:
            layout.setSpacing(5)
            layout.setContentsMargins(8, 8, 8, 8)

        
        tools_panel.addWidget(self.EditScopesBtn)

        self.InfoBox.setMaximumHeight(50)
        
        self.gridLayout_2.setRowStretch(0, 3)
        self.gridLayout_2.setRowStretch(1, 1)
        self.gridLayout_2.setRowStretch(2, 1)
        self.gridLayout_2.setRowStretch(3, 1)

    def init_components(self):
        self.Init_UI_Win()
        self.Init_UI_GunsData()
        self.Init_UI_Btn()
        self.load_crouch_key()
        self.setup_listen_mode_handlers()
        
        # Add info about middle mouse button toggle and F9 key
        self.Init_UI_LOG("INFO: Macro toggle is set to MIDDLE MOUSE BUTTON")
        self.Init_UI_LOG("INFO: Press F9 to toggle Tab key functionality")
        
        saved_key = PC.get_config_data('crouch_key')
        if saved_key:
            index = self.CrouchKeyCombo.findText(saved_key)
            if index >= 0:
                self.CrouchKeyCombo.setCurrentIndex(index)
        
        for widget in ['ScopeMode', 'IsScope', 'EquipBox', 'PoseBox']:
            if hasattr(self, widget):
                getattr(self, widget).setVisible(False)
        
        saved_ads = PC.get_config_data('ads_sensitivity') 
        if saved_ads:
            self.ADSSpinBox.setValue(saved_ads)
            
        saved_vertical = PC.get_config_data('vertical_multiplier')
        if saved_vertical:
            self.VerticalSpinBox.setValue(saved_vertical)
        
        saved_scope = PC.get_config_data('scope_multiplier')
        if saved_scope:
            self.ScopeSpinBox.setValue(saved_scope)

        try:
            with open('./Config/overlay_position.json', 'r') as f:
                pos = json.load(f)
                self.OverlayXSpinBox.setValue(pos.get('x', self.OverlayXSpinBox.value()))
                self.OverlayYSpinBox.setValue(pos.get('y', self.OverlayYSpinBox.value()))
        except:
            pass

    def Init_UI_Btn(self):
        self.Startbtn.clicked.connect(self.start)
        self.Stopbtn.clicked.connect(self.stop)
        self.Pausebtn.clicked.connect(self.pause)
        self.ResolutionBtn.clicked.connect(self.Save_Config_Resolution)
        self.CrouchKeyCombo.currentTextChanged.connect(self.on_crouch_key_changed)
    
    def Init_UI_Win(self):
        if PC.window_version:
            version = "(Win11)"
        else:
            version = "(Win10)"
        
        self.WinVersion.setText(version)
    
    def toggle_window(self):
        if self.isHidden:
            self.show()
            self.isHidden = False
        else:
            self.hide()
            self.isHidden = True
    
    def Get_GUNS_CH(self, Name, Type):
        CH_data = fire_data.ACCESSORIES_CH.get(Type, None)
        Default = Name if Type == "Name" else "Unknown"
        if CH_data:
            return CH_data.get(Name.lower(), Default)
        else:
            return "Unknown"
    
    def Init_UI_GunsData(self):
        results = PC.get_guns_result()
        for idx, result in enumerate(results, start=1):
            if result:
                self.__getattribute__(f"Name{idx}Name").setText(self.Get_GUNS_CH(result["Name"], "Name"))
                self.__getattribute__(f"Scope{idx}Name").setText(self.Get_GUNS_CH(result["Scope"], "Scope"))
                self.__getattribute__(f"Muzzle{idx}Name").setText(self.Get_GUNS_CH(result["Muzzle"], "Muzzle"))
                self.__getattribute__(f"Grip{idx}Name").setText(self.Get_GUNS_CH(result["Grip"], "Grip"))
                self.__getattribute__(f"Butt{idx}Name").setText(self.Get_GUNS_CH(result["Stock"], "Stock"))
    
    def Init_UI_ReductionData(self):
        self.Init_UI_GunsData() 
    
    def Init_UI_LOG(self, info):
        self.Info.append(info + "\n")
    
    def Save_Config_Resolution(self):
        PC.Monitor = self.ResolutionSelect.currentText()
        PC.save_config_data(True, PC.Monitor)
        self.message_Info("Resolution saved")
    
    def message_Info(self, message, title="Quick message"):
        message_box = QMessageBox()
        message_box.setWindowTitle(title)
        message_box.setText(message)
        message_box.setIcon(QMessageBox.Information)
        message_box.setWindowFlags(message_box.windowFlags() | Qt.WindowStaysOnTopHint)
        message_box.exec()
        
    def is_numeric(self, string):
        try:
            if '.' in string:
                return float(string)
            else:
                return int(string)
        except ValueError:
            return False
    
    def start(self):
        self.my_key_thread = AppMainKeyListener(PC)
        self.my_mouse_thread = AppMainMouseListener(PC)
        self.my_key_thread.start()
        self.my_mouse_thread.start()
        
        self.my_key_thread.keyInfo.connect(self.onKeyPressed)
        self.my_mouse_thread.mouseClicked.connect(self.onKeyPressed)
        
        macro.start_program()
        
        self.SetStatus()
        self.StatusInfo.setText('Program started.....')
    
    def SetStatus(self):
        self.Startbtn.setEnabled(False)
        self.Pausebtn.setEnabled(True)
        self.Stopbtn.setEnabled(True)
        self.EquipBox.setEnabled(True)
        self.PoseBox.setEnabled(True)
        self.IsScope.setEnabled(True)
    
    def stop(self):
        if self.crosshair:
            self.crosshair.close()
        if PC.overlay:
            PC.hide_overlay()
            
        self.StatusInfo.setText('Program shutting down....')
        
        # Properly stop the listeners
        if self.my_key_thread and self.my_key_thread.isRunning():
            self.my_key_thread.stop_listener()
            self.my_key_thread.terminate()
            self.my_key_thread.wait(1000)
            
        if self.my_mouse_thread and self.my_mouse_thread.isRunning():
            self.my_mouse_thread.stop_listener()
            self.my_mouse_thread.terminate()
            self.my_mouse_thread.wait(1000)
        
        QApplication.quit()
    
    def pause(self):
        if self.pauses:
            self.my_key_thread.stop_listener()
            self.my_mouse_thread.stop_listener()
            self.Pausebtn.setText("Continue")
            self.StatusInfo.setText('Program paused....')
            self.pauses = False
        else:
            self.my_key_thread.rerun()
            self.my_mouse_thread.rerun()
            self.Pausebtn.setText("Pause")
            self.StatusInfo.setText('Program running....')
            self.pauses = True
    
    def onKeyPressed(self, key, value):
        try:
            # Handle case when value might be None or empty
            if value is None or len(value) == 0:
                values = None
            else:
                values = value[0]
                
            # Define action map with safer approach
            actions = {
                "l": (self.Init_UI_LOG, [values] if values is not None else [""]),
                "g": (self.Init_UI_GunsData, []),
                "c": (self.Init_UI_ReductionData, []),
                "t": (self.toggle_window, []),
                "s": (self.update_fire_status, [values] if values is not None else [False]),
                "p": (self.update_posture, [values] if values is not None else ["None"]),
                "ctrl": (self.update_ctrl_status, [values] if values is not None else [False]),
                "shift": (self.update_shift_status, [values] if values is not None else [False]),
                "mag": (lambda x=None: None, [])
            }
            
            action, args = actions.get(key, (None, None))
            if action:
                action(*args)
        except Exception as e:
            import traceback
            print(f"Error in onKeyPressed handler: {e}")
            print(traceback.format_exc())
            
    def update_ctrl_status(self, is_pressed):
        # Update control key status
        if hasattr(PC, 'crouch_pressed'):
            PC.crouch_pressed = is_pressed

    def update_shift_status(self, is_pressed):
        # Update shift key status
        if hasattr(PC, 'shift_pressed'):
            PC.shift_pressed = is_pressed

    def update_fire_status(self, is_firing):
        # Update fire status
        if hasattr(PC, 'StartFire'):
            PC.StartFire = is_firing
            
    def update_posture(self, posture):
        # Update posture
        if hasattr(PC, 'Current_posture'):
            PC.Current_posture = posture

    def on_crouch_key_changed(self, key):
        PC.set_crouch_key(key)

    def save_crouch_key(self):
        key = self.CrouchKeyCombo.currentText()
        PC.set_crouch_key(key)
        PC.save_config_data('crouch_key', key)
        self.message_Info("Crouch key saved")
        
    def load_crouch_key(self):
        key = PC.get_config_data('crouch_key') 
        if key:
            index = self.CrouchKeyCombo.findText(key)
            if index >= 0:
                self.CrouchKeyCombo.setCurrentIndex(index)
                PC.set_crouch_key(key)

    def open_weapon_editor(self):
        try:
            editor = WeaponEditor(self)
            result = editor.exec_()
            if result == QDialog.Accepted:
                self.Init_UI_LOG("Recoil patterns updated")
                macro.load_weapon_patterns()
            else:
                self.Init_UI_LOG("Pattern editing canceled")
        except Exception as e:
            self.Init_UI_LOG(f"Error opening editor: {str(e)}")

    def setup_listen_mode_handlers(self):
        self.ListenBothRadio.toggled.connect(lambda: self.on_listen_mode_changed("both"))
        self.ListenOneRadio.toggled.connect(lambda: self.on_listen_mode_changed("1"))
        self.ListenTwoRadio.toggled.connect(lambda: self.on_listen_mode_changed("2"))
        
    def on_listen_mode_changed(self, mode):
        if self.ListenBothRadio.isChecked() or self.ListenOneRadio.isChecked() or self.ListenTwoRadio.isChecked():
            PC.listen_mode = mode
            PC.save_config_data('listen_mode', mode)
            self.Init_UI_LOG(f"Listening mode changed to: {mode}")

    def toggle_crosshair(self):
        if self.CrosshairBtn.isChecked():
            self.show_crosshair()
            PC.crosshair_enabled = True
            self.save_crosshair_settings()
        else:
            self.hide_crosshair()
            PC.crosshair_enabled = False
        PC.save_config_data('crosshair', PC.crosshair_enabled)

    def show_crosshair(self):
        if not self.crosshair:
            self.crosshair = Crosshair()
        
        try:
            resolution = self.CrosshairResolutionCombo.currentText()
            self.crosshair.set_resolution(resolution)
            self.crosshair.show()
        except Exception as e:
            print(f"Error showing crosshair: {e}")
            self.Init_UI_LOG("Error showing crosshair")

    def hide_crosshair(self):
        if self.crosshair:
            self.crosshair.hide()

    def save_crosshair_settings(self):
        if self.crosshair:
            resolution = self.CrosshairResolutionCombo.currentText()
            PC.save_config_data('crosshair_resolution', resolution)
            self.crosshair.set_resolution(resolution)

            type_map = {"Simple": "simple"}
            crosshair_type = type_map.get(self.CrosshairTypeCombo.currentText(), "simple")
            self.crosshair.set_crosshair_type(crosshair_type)
            PC.save_config_data('crosshair_type', crosshair_type)

            color_map = {"White": "white", "Red": "red", "Green": "green", "Blue": "blue"}
            color = color_map.get(self.CrosshairColorCombo.currentText(), "white")
            self.crosshair.set_crosshair_color(color)
            PC.save_config_data('crosshair_color', color)
            
            self.message_Info("Crosshair settings saved")
            
    def on_crosshair_type_changed(self, type_name):
        if self.crosshair:
            type_map = {
                "Simple": "simple",
            }
            crosshair_type = type_map.get(type_name, "simple")
            self.crosshair.set_crosshair_type(crosshair_type)
            self.CrosshairColorCombo.setEnabled(crosshair_type != "rainbow_swastika")
            PC.save_config_data('crosshair_type', crosshair_type)

    def on_crosshair_color_changed(self, color_name):
        if self.crosshair:
            color_map = {"White": "white", "Red": "red", "Green": "green", "Blue": "blue"}
            self.crosshair.set_crosshair_color(color_map.get(color_name, "white"))
            PC.save_config_data('crosshair_color', color_map.get(color_name, "white"))

    def on_crosshair_size_changed(self, value):
        if self.crosshair:
            try:
                self.crosshair.set_crosshair_size(value)
                PC.save_config_data('crosshair_size', value)
            except Exception as e:
                print(f"Error setting crosshair size: {e}")

    def get_color_text(self, color):
        color_map = {
            "white": "White",
            "red": "Red",
            "green": "Green",
            "blue": "Blue"
        }
        return color_map.get(color, "White")

    def toggle_overlay(self):
        if self.OverlayBtn.isChecked():
            # Save current scope values before enabling overlay
            custom_scope_values = {}
            if hasattr(PC, 'ScopeData'):
                for scope, value in PC.ScopeData.items():
                    if scope in ['8x', '8bei'] and value != 8.0:
                        custom_scope_values[scope] = value
                        print(f"Saving custom scope value before enabling overlay: {scope} = {value}")
            
            # Make sure to reload scope settings before enabling overlay
            PC.reload_scope_config()
            
            # Restore custom scope values
            for scope, value in custom_scope_values.items():
                print(f"Restoring custom scope value: {scope} = {value}")
                PC.ScopeData[scope] = value
                # Also update macro
                import macro
                macro.scope_multipliers[scope] = value
            
            PC.overlay_enabled = True
            PC.show_overlay()
            self.Init_UI_LOG("Overlay enabled")
        else:
            PC.overlay_enabled = False
            PC.hide_overlay()
            self.Init_UI_LOG("Overlay disabled")
        PC.save_config_data('overlay', PC.overlay_enabled)

    def reset_overlay_position(self):
        if PC.overlay:
            PC.overlay.reset_position()
            self.Init_UI_LOG("Overlay position reset")
        else:
            self.Init_UI_LOG("Overlay not active")

    def update_overlay_info(self):
        if PC.overlay and PC.overlay.isVisible():
            weapon1 = self.gun1_data["name"] if self.gun1_data else "Нет оружия"
            weapon2 = self.gun2_data["name"] if self.gun2_data else "Нет оружия"
            PC.overlay.update_info(weapon1, weapon2, macro.macro_enabled.is_set())

    def change_overlay_opacity(self, value):
        if PC.overlay:
            opacity = value / 100.0
            PC.overlay.setWindowOpacity(opacity)
            PC.save_config_data('overlay_opacity', value)

    def on_crouch_toggle_changed(self, state):
        PC.set_crouch_toggle_mode(bool(state))
        self.Init_UI_LOG(f"Crouch mode: {'Toggle' if state else 'Hold'}")

    def on_ads_changed(self, value):
        macro.set_ads_multiplier(value)
        PC.save_config_data('ads_sensitivity', value)
        self.Init_UI_LOG(f"ADS sensitivity: {value} ({value/50:.2f}x)")

    def on_vertical_changed(self, value):
        macro.set_vertical_multiplier(value)
        PC.save_config_data('vertical_multiplier', value)
        self.Init_UI_LOG(f"Vertical multiplier: {value:.1f}x)")

    def on_scope_changed(self, value):
        macro.set_scope_multiplier(value)
        PC.save_config_data('scope_multiplier', value)
        self.Init_UI_LOG(f"Scope multiplier: {value} ({40.0/value:.2f}x)")

    def change_overlay_position(self, _=None):
        if PC.overlay:
            x = self.OverlayXSpinBox.value()
            y = self.OverlayYSpinBox.value()
            PC.overlay.move(x, y)
            PC.overlay.save_position()

    def open_scope_editor(self):
        try:
            editor = ScopeEditor(self)
            result = editor.exec_()
            if result == QDialog.Accepted:
                self.Init_UI_LOG("Scope multipliers updated")
                PC.reload_scope_config()
            else:
                self.Init_UI_LOG("Scope editing canceled")
        except Exception as e:
            self.Init_UI_LOG(f"Error opening editor: {str(e)}")

    def on_crosshair_resolution_changed(self, resolution):
        if self.crosshair:
            self.crosshair.set_resolution(resolution)
            PC.save_config_data('crosshair_resolution', resolution)
            self.Init_UI_LOG(f"Crosshair resolution changed to: {resolution}")

if __name__ == '__main__':
    app = QApplication([])
    PC = Process.ProcessClass()
    Main = AppManager()
    Main.show()
    sys.exit(app.exec_())