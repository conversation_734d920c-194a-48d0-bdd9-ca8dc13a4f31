from threading import Thread
from PyQt5.QtCore import QThread, pyqtSignal
from pynput import keyboard
from pynput.keyboard import Key
import macro
import win32api
import win32con
import time

class AppMainKeyListener(QThread):
    keyInfo = pyqtSignal(str, tuple)

    def __init__(self, PCdata):
        super().__init__()
        self.KeyHook = None
        self.PC = PCdata
        self.debug = False
        self.disable_tab_state = False
        self.isDisableTab = False
    def print_debug(self, message):
        if self.debug:
            print(message)

    def on_key_pressed(self, key):
        try:
            # Extract key name with robust error handling
            key_name = None
            try:
                if hasattr(key, 'name'):
                    key_name = key.name
                elif hasattr(key, 'char'):
                    key_name = key.char
                else:
                    key_name = str(key).replace("'", "")
            except:
                # Fallback for special cases
                key_name = str(key).replace("'", "")
            
            if key_name is None:
                # Skip if we couldn't extract a valid key name
                return

            # Check control key state explicitly
            ctrl_pressed = win32api.GetKeyState(win32con.VK_CONTROL) < 0 or win32api.GetKeyState(0xA2) < 0 or win32api.GetKeyState(0xA3) < 0
            shift_pressed = win32api.GetKeyState(win32con.VK_SHIFT) < 0 or win32api.GetKeyState(0xA0) < 0 or win32api.GetKeyState(0xA1) < 0
            
            # Debug output - always show key presses for debugging
      

            # Note: Caps Lock macro toggle has been moved to middle mouse button
            
            if key_name == "f9":
                disable_tab_state = macro.toggle_disable_tab()
                # Update overlay with the correct tab status
                if hasattr(self.PC, 'overlay') and self.PC.overlay and self.PC.overlay.isVisible():
                    # Pass the correct tab status to the overlay
                    self.PC.overlay.update_info(
                        tab_status=disable_tab_state["enabled"]
                    )
                    print(f"Updated overlay tab status to: {disable_tab_state['enabled']}")
                
                if disable_tab_state["enabled"]:
                    self.keyInfo.emit('l', (disable_tab_state["status"],))
                    self.isDisableTab = False
                else:
                    self.keyInfo.emit('l', (disable_tab_state["status"],))
                    self.isDisableTab = True

            # Handle tab key for weapon recognition
            elif key_name == "tab":
                # Check if tab functionality is disabled
                # When disabled_tab.is_set() is True, tab functionality should be disabled
                if macro.disabled_tab.is_set():
                    print("Tab functionality is disabled")
                    self.keyInfo.emit('l', ("Tab functionality is disabled",))
                else:
                    self.PC.StartFire = False
                    Thread(target=self.PC.recognize_all_guns_info, args=(self.keyInfo.emit,)).start()
                    self.keyInfo.emit('s', (self.PC.StartFire,))
            
            # Direct check for number keys 1 and 2 with modifiers
            elif key_name in ["1", "2"]:
                # Always switch weapons regardless of modifier keys
                print(f"Number key pressed: {key_name}, CTRL: {ctrl_pressed}, SHIFT: {shift_pressed}")
                
                # Weapon switch with appropriate debug message
                if ctrl_pressed:
                    print(f"Switching weapon to slot {key_name} while crouching (CTRL pressed)")
                    # Ensure crouch state is set
                    self.PC.Current_posture = "c"
                    self.PC.crouch_pressed = True
                    self.keyInfo.emit('p', (self.PC.Current_posture,))
                    self.keyInfo.emit('ctrl', (True,))
                    
                    # Emit log message for debugging
                    self.keyInfo.emit('l', (f"CTRL+{key_name} pressed: Switching to weapon slot {key_name} while crouching",))
                elif shift_pressed:
                    print(f"Switching weapon to slot {key_name} while running (SHIFT pressed)")
                    self.keyInfo.emit('l', (f"SHIFT+{key_name} pressed: Switching to weapon slot {key_name} while running",))
                else:
                    print(f"Switching weapon to slot {key_name}")
                
                # Do the weapon switch
                result = self.PC.Change_firearms(key_name)
                self.keyInfo.emit('g', (None,))
                if result:
                    self.keyInfo.emit('l', (result,))
                
            # Handle shift+number combinations
            elif key_name in ["!", "@", "exclam", "at"]:
                # Convert to normal number for weapon switching
                if key_name in ["!", "exclam"]:
                    weapon_key = "1"
                elif key_name in ["@", "at"]:
                    weapon_key = "2"
                
                print(f"Switching weapon to slot {weapon_key} while running (SHIFT+number)")
                
                # Do the weapon switch
                result = self.PC.Change_firearms(weapon_key)
                self.keyInfo.emit('g', (None,))
                if result:
                    self.keyInfo.emit('l', (result,))
                
            elif key_name in ["z", "c"] or key_name == "space":
                self.PC.Change_posture(key_name)
                self.keyInfo.emit('p', (self.PC.Current_posture,))
            elif key_name in ["ctrl_l", "ctrl", "control_l", "control"]:
                self.PC.Current_posture = "c"
                self.keyInfo.emit('p', (self.PC.Current_posture,))
                self.keyInfo.emit('ctrl', (True,))
                if hasattr(self.PC, 'on_crouch_pressed'):
                    self.PC.on_crouch_pressed()
            elif key_name in ["shift", "shift_l"]:
                if hasattr(self.PC, 'on_shift_pressed'):
                    self.PC.on_shift_pressed()
                self.keyInfo.emit('shift', (True,))
            elif key_name in ["alt_l", "alt"]:
                self.keyInfo.emit('mag', (True,))
            elif key_name == "v":
                self.keyInfo.emit('mag', (True,))
            elif key_name == "insert":
                if hasattr(self.PC, 'reduction_data'):
                    self.PC.reduction_data()
                self.keyInfo.emit('c', (None,))
            elif key_name == "home":
                self.keyInfo.emit('t', (None,))
                
        except Exception as e:
            import traceback
            print(f"Error in key press handler: {e}")
            print(traceback.format_exc())
            if self.debug:
                print(f"Key that caused error: {key}")

    def on_key_release(self, key):
        try:
            # Extract key name with robust error handling
            key_name = None
            try:
                if hasattr(key, 'name'):
                    key_name = key.name
                elif hasattr(key, 'char'):
                    key_name = key.char
                else:
                    key_name = str(key).replace("'", "")
            except:
                # Fallback for special cases
                key_name = str(key).replace("'", "")
            
            if key_name is None:
                # Skip if we couldn't extract a valid key name
                return

            # Debug output
            if self.debug:
                print(f"Key released: {key_name} (original: {key})")
                
            # Check if any number keys were just released while CTRL was still pressed
            ctrl_still_pressed = win32api.GetKeyState(win32con.VK_CONTROL) < 0 or win32api.GetKeyState(0xA2) < 0 or win32api.GetKeyState(0xA3) < 0
            
            # For number keys, don't change posture if CTRL is still pressed
            if key_name in ["1", "2"] and ctrl_still_pressed:
                print(f"Released {key_name} but keeping crouch state because CTRL is still pressed")
                return
            
            if key_name in ["ctrl_l", "ctrl", "control_l", "control"]:
                # Check if a weapon switch was just performed
                number_key_pressed = win32api.GetKeyState(0x31) < 0 or win32api.GetKeyState(0x32) < 0  # 1 or 2 keys
                
                if not number_key_pressed:
                    print("CTRL released, resetting posture to standing")
                    self.PC.Current_posture = "None"
                    self.keyInfo.emit('p', (self.PC.Current_posture,))
                    self.keyInfo.emit('ctrl', (False,))
                    if hasattr(self.PC, 'on_crouch_released'):
                        self.PC.on_crouch_released()
            elif key_name in ["shift", "shift_l"]:
                if hasattr(self.PC, 'on_shift_released'):
                    self.PC.on_shift_released()
                self.keyInfo.emit('shift', (False,))
            elif key_name in ["alt_l", "alt"]:
                self.keyInfo.emit('mag', (False,))
            elif key_name == "v":
                self.keyInfo.emit('mag', (False,))
                
        except Exception as e:
            import traceback
            print(f"Error in key release handler: {e}")
            print(traceback.format_exc())
            if self.debug:
                print(f"Key that caused error: {key}")

    def run(self):
        """Start keyboard monitoring via pynput"""
        self.rerun()

    def rerun(self):
        """Restart monitoring"""
        self.KeyHook = keyboard.Listener(on_press=self.on_key_pressed, on_release=self.on_key_release)
        self.KeyHook.start()
        self.keyInfo.emit('l', ("Keyboard monitoring started...",))

    def stop_listener(self):
        """Stop monitoring"""
        if self.KeyHook:
            self.KeyHook.stop()
            self.KeyHook = None
        self.keyInfo.emit('l', ("Keyboard monitoring stopped.....",))
