#!/usr/bin/env python3
"""
Comprehensive validation of muzzle detection accuracy improvements.
This script tests that slot 1 and slot 2 now have equal muzzle detection accuracy.
"""

import cv2
import numpy as np
import os
import asyncio
from recognition import (
    enhanced_muzzle_matching, 
    normalize_image, 
    get_optimal_template_size,
    template_match_fallback,
    sift_matching_normalized,
    orb_matching
)
from resolution_setting import RESOLUTION_SETTINGS

class AccuracyValidator:
    def __init__(self, resolution='1920x1080'):
        self.resolution = resolution
        self.regions = RESOLUTION_SETTINGS[resolution]
        self.muzzle_templates = self._load_muzzle_templates()
        
    def _load_muzzle_templates(self):
        """Load muzzle templates"""
        templates = {}
        muzzle_path = "_internal/data/firearms/Muzzle/"
        
        if os.path.exists(muzzle_path):
            for filename in os.listdir(muzzle_path):
                if filename.endswith('.png'):
                    template_path = os.path.join(muzzle_path, filename)
                    img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        templates[filename[:-4]] = img
        return templates
    
    def test_enhanced_matching_algorithm(self):
        """Test the enhanced muzzle matching algorithm"""
        print("Testing enhanced muzzle matching algorithm...")
        print("=" * 60)
        
        # Create test ROIs for both slots
        roi1_size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                    self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        roi2_size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                    self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        # Test scenarios
        test_scenarios = [
            ("identical_rois", self._create_identical_rois),
            ("high_contrast", self._create_high_contrast_rois),
            ("low_contrast", self._create_low_contrast_rois),
            ("noisy", self._create_noisy_rois),
            ("realistic", self._create_realistic_rois)
        ]
        
        results = {}
        
        for scenario_name, roi_generator in test_scenarios:
            print(f"\nTesting scenario: {scenario_name}")
            
            roi1, roi2 = roi_generator(roi1_size, roi2_size)
            
            # Test against all templates
            slot1_scores = []
            slot2_scores = []
            
            for template_name, template_img in self.muzzle_templates.items():
                score1 = enhanced_muzzle_matching(roi1, template_img)
                score2 = enhanced_muzzle_matching(roi2, template_img)
                
                slot1_scores.append(score1)
                slot2_scores.append(score2)
                
                print(f"  {template_name:20}: Slot1={score1:.4f}, Slot2={score2:.4f}, Diff={abs(score1-score2):.4f}")
            
            avg_score1 = np.mean(slot1_scores)
            avg_score2 = np.mean(slot2_scores)
            avg_diff = abs(avg_score1 - avg_score2)
            
            results[scenario_name] = {
                'slot1_avg': avg_score1,
                'slot2_avg': avg_score2,
                'avg_difference': avg_diff,
                'max_difference': max(abs(s1 - s2) for s1, s2 in zip(slot1_scores, slot2_scores))
            }
            
            print(f"  Average scores: Slot1={avg_score1:.4f}, Slot2={avg_score2:.4f}")
            print(f"  Average difference: {avg_diff:.4f}")
            print(f"  Max difference: {results[scenario_name]['max_difference']:.4f}")
            
            if avg_diff < 0.02:  # 2% threshold
                print(f"  ✓ {scenario_name}: Accuracy difference within acceptable range")
            else:
                print(f"  ⚠️  {scenario_name}: Significant accuracy difference!")
        
        return results
    
    def _create_identical_rois(self, size1, size2):
        """Create identical ROIs for both slots"""
        # Create a structured pattern
        roi = np.zeros(size1, dtype=np.uint8)
        cv2.rectangle(roi, (5, 5), (size1[1]-5, size1[0]-5), 128, 2)
        cv2.circle(roi, (size1[1]//2, size1[0]//2), min(size1)//6, 255, -1)
        
        # For slot 2, create the same pattern (should be identical since sizes are the same)
        roi2 = roi.copy()
        
        return roi, roi2
    
    def _create_high_contrast_rois(self, size1, size2):
        """Create high contrast ROIs"""
        roi1 = np.zeros(size1, dtype=np.uint8)
        roi1[size1[0]//4:3*size1[0]//4, size1[1]//4:3*size1[1]//4] = 255
        
        roi2 = np.zeros(size2, dtype=np.uint8)
        roi2[size2[0]//4:3*size2[0]//4, size2[1]//4:3*size2[1]//4] = 255
        
        return roi1, roi2
    
    def _create_low_contrast_rois(self, size1, size2):
        """Create low contrast ROIs"""
        roi1 = np.full(size1, 120, dtype=np.uint8)
        roi1[size1[0]//4:3*size1[0]//4, size1[1]//4:3*size1[1]//4] = 140
        
        roi2 = np.full(size2, 120, dtype=np.uint8)
        roi2[size2[0]//4:3*size2[0]//4, size2[1]//4:3*size2[1]//4] = 140
        
        return roi1, roi2
    
    def _create_noisy_rois(self, size1, size2):
        """Create noisy ROIs with same noise characteristics"""
        np.random.seed(42)  # Fixed seed for reproducible results
        roi1 = np.random.randint(0, 255, size1, dtype=np.uint8)
        
        np.random.seed(42)  # Same seed for slot 2
        roi2 = np.random.randint(0, 255, size2, dtype=np.uint8)
        
        return roi1, roi2
    
    def _create_realistic_rois(self, size1, size2):
        """Create realistic-looking muzzle ROIs"""
        # Create a muzzle-like pattern
        roi1 = np.full(size1, 80, dtype=np.uint8)
        # Add some geometric shapes that might represent a muzzle
        cv2.rectangle(roi1, (10, 15), (size1[1]-10, size1[0]-15), 150, -1)
        cv2.circle(roi1, (size1[1]//2, size1[0]//2), 8, 200, -1)
        
        roi2 = np.full(size2, 80, dtype=np.uint8)
        cv2.rectangle(roi2, (10, 15), (size2[1]-10, size2[0]-15), 150, -1)
        cv2.circle(roi2, (size2[1]//2, size2[0]//2), 8, 200, -1)
        
        return roi1, roi2
    
    def test_individual_algorithms(self):
        """Test individual algorithm components"""
        print("\nTesting individual algorithm components...")
        print("=" * 60)
        
        # Create test data
        roi1_size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                    self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        roi2_size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                    self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        test_roi1 = self._create_realistic_rois(roi1_size, roi2_size)[0]
        test_roi2 = self._create_realistic_rois(roi1_size, roi2_size)[1]
        
        # Test with first template
        template = list(self.muzzle_templates.values())[0]
        template_name = list(self.muzzle_templates.keys())[0]
        
        print(f"Testing with template: {template_name}")
        
        # Test normalization
        norm_roi1 = normalize_image(test_roi1)
        norm_roi2 = normalize_image(test_roi2)
        norm_template = normalize_image(template)
        
        print(f"Original ROI1 range: {test_roi1.min()}-{test_roi1.max()}")
        print(f"Normalized ROI1 range: {norm_roi1.min()}-{norm_roi1.max()}")
        print(f"Original ROI2 range: {test_roi2.min()}-{test_roi2.max()}")
        print(f"Normalized ROI2 range: {norm_roi2.min()}-{norm_roi2.max()}")
        
        # Test optimal template sizing
        opt_template1 = get_optimal_template_size(norm_template, test_roi1.shape)
        opt_template2 = get_optimal_template_size(norm_template, test_roi2.shape)
        
        print(f"Original template size: {template.shape}")
        print(f"Optimal template size for ROI1: {opt_template1.shape}")
        print(f"Optimal template size for ROI2: {opt_template2.shape}")
        
        # Test individual algorithms
        algorithms = [
            ("Template Matching", template_match_fallback),
            ("SIFT Normalized", sift_matching_normalized),
            ("ORB Matching", orb_matching)
        ]
        
        for algo_name, algo_func in algorithms:
            try:
                score1 = algo_func(norm_roi1, opt_template1)
                score2 = algo_func(norm_roi2, opt_template2)
                diff = abs(score1 - score2)
                
                print(f"{algo_name:18}: Slot1={score1:.4f}, Slot2={score2:.4f}, Diff={diff:.4f}")
                
                if diff < 0.05:
                    print(f"  ✓ {algo_name}: Consistent between slots")
                else:
                    print(f"  ⚠️  {algo_name}: Inconsistent between slots")
                    
            except Exception as e:
                print(f"{algo_name:18}: Error - {e}")
    
    def test_template_scaling_consistency(self):
        """Test that template scaling produces consistent results"""
        print("\nTesting template scaling consistency...")
        print("=" * 60)
        
        roi1_size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                    self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        roi2_size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                    self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        # Create identical test ROIs
        test_roi1, test_roi2 = self._create_identical_rois(roi1_size, roi2_size)
        
        scaling_consistency = []
        
        for template_name, template in self.muzzle_templates.items():
            # Test optimal scaling for both ROIs
            opt_template1 = get_optimal_template_size(template, test_roi1.shape)
            opt_template2 = get_optimal_template_size(template, test_roi2.shape)
            
            # Templates should be the same size since ROIs are the same size
            size_consistent = opt_template1.shape == opt_template2.shape
            
            if size_consistent:
                # Test matching scores
                score1 = template_match_fallback(test_roi1, opt_template1)
                score2 = template_match_fallback(test_roi2, opt_template2)
                score_diff = abs(score1 - score2)
                
                scaling_consistency.append(score_diff)
                
                print(f"{template_name:20}: Sizes consistent, Score diff: {score_diff:.4f}")
            else:
                print(f"{template_name:20}: ⚠️  Size inconsistency!")
                print(f"  Template1 size: {opt_template1.shape}")
                print(f"  Template2 size: {opt_template2.shape}")
        
        if scaling_consistency:
            avg_consistency = np.mean(scaling_consistency)
            max_inconsistency = max(scaling_consistency)
            
            print(f"\nScaling consistency metrics:")
            print(f"  Average score difference: {avg_consistency:.4f}")
            print(f"  Maximum score difference: {max_inconsistency:.4f}")
            
            if avg_consistency < 0.01 and max_inconsistency < 0.05:
                print("  ✓ Template scaling is highly consistent")
                return True
            else:
                print("  ⚠️  Template scaling shows inconsistencies")
                return False
        
        return False

def main():
    """Main validation function"""
    print("MUZZLE DETECTION ACCURACY VALIDATION")
    print("=" * 60)
    
    validator = AccuracyValidator()
    
    # Run all validation tests
    algorithm_results = validator.test_enhanced_matching_algorithm()
    validator.test_individual_algorithms()
    scaling_consistent = validator.test_template_scaling_consistency()
    
    # Overall assessment
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    passed_scenarios = 0
    total_scenarios = len(algorithm_results)
    
    for scenario, results in algorithm_results.items():
        if results['avg_difference'] < 0.02:  # 2% threshold
            passed_scenarios += 1
            print(f"✓ {scenario}: PASSED (diff: {results['avg_difference']:.4f})")
        else:
            print(f"⚠️  {scenario}: FAILED (diff: {results['avg_difference']:.4f})")
    
    success_rate = (passed_scenarios / total_scenarios) * 100
    print(f"\nOverall success rate: {success_rate:.1f}% ({passed_scenarios}/{total_scenarios})")
    
    if success_rate >= 80 and scaling_consistent:
        print("🎉 VALIDATION SUCCESSFUL: Muzzle detection accuracy is now equalized!")
    else:
        print("❌ VALIDATION FAILED: Further improvements needed")
    
    print("\nRecommendations:")
    if success_rate < 80:
        print("- Fine-tune algorithm weights in enhanced_muzzle_matching")
        print("- Adjust confidence thresholds for specific scenarios")
    if not scaling_consistent:
        print("- Review template scaling algorithm")
        print("- Ensure consistent preprocessing for both slots")
    
    print("- Test with real game data to validate improvements")
    print("- Monitor console output during actual gameplay")

if __name__ == "__main__":
    main()
