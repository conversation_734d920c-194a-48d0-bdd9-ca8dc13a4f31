import threading
import time
import win32api
import win32con
import json

macro_enabled = threading.Event()
disabled_tab = threading.Event()
vertical_sensitivity_multiplier = 1.0
resolution_sensitivity_multiplier = 1.0

# Хранилище паттернов для различных оружий
weapon_patterns = {
    "none": 0.0,
    "tangmuxunchong<PERSON>": 0.0,
    "zidongzhuangtianbuqiang": 0.0,

    "mg3": 2.7,
    "dp28": 5,
    "m249": 4.2,

    "m762": 6.9,
    "ace32": 5.43,
    "aug": 5.9,
    "famas": 5.6,
    "g36c": 5.6,
    "m416": 5.1,
    "groza": 5.1,
    "scar-l": 4.53,
    "qbz": 4.62,
    "mk47": 3.15,
    "m16a4": 3.15,
    "akm": 5.56,
    "k2": 4.8,

    "mp5k":3,
    "p90": 3,
    "micro_uzi": 3,
    "js9": 3.9,
    "pp19": 3.4,
    "vector": 6.6,
    "tommy_gun": 4,
    "mp9": 2.6,
    "ump45": 4.2,

    "sks":4,
    "mk12":4,
    "mk14":6,
    "qbu":4,
    "vss":7,
    "mini14":4,


}

# Множители для прицелов
scope_multipliers = {
    'none': 1.0,
    'hongdian': 1.0,     # Red dot
    'quanxi': 1.0,       # Holographic
    '2bei': 2.2,
    '3bei': 3.25,
    '4bei': 4.0,
    '6bei': 6.0,
    '8bei': 8.0,
    '15bei': 15.0,
    # Add alternative format
    '2x': 2.2,
    '3x': 3.25,
    '4x': 4.0,
    '6x': 6.0,
    '8x': 8.0,
    '15x': 15.0
}

# Обновляем множители для ручек - теперь все значения в десятичных дробях для прямого умножения
grip_multipliers = {
    "none": 1.0,  # Без ручки - без изменений
    "banjieshi": 0.92,  # Half grip уменьшает отдачу на 8%
    "muzhi": 0.90,     # Thumb grip уменьшает отдачу на 10%
    "chuizhi": 0.85,   # Vertical grip уменьшает отдачу на 15%
}

# Обновляем множители для дульных тормозов
muzzle_multipliers = {
    "none": 1.0,  # Без тормоза - без изменений
    "buqiangbuchang": 0.85,  # Compensator уменьшает отдачу на 15%
    "buqiangxiaoyan": 0.90,  # Flash hider уменьшает отдачу на 10%
    "brake": 0.92,
    "jujiqiangbuchang": 0.8,
    "svpl":0.9,
    "chongfengqiangbuchang":0.75,
    "chongFengQiangXIaoYan":0.9,
}

# Обновляем множители для прикладов
stock_multipliers = {
    "none": 1.0,  # Без приклада - без изменений
    "zhanshuqiangtuo": 0.90,  # Tactical stock уменьшает отдачу на 10%
    "zhongxinqiangtuo": 0.90,  # Другие приклады без изменений
    "tuosaiban": 0.8,
    "zhedieshiqiangtuo": 0.8,
}
# Обновляем множители для стойки и sprint с точными значениями
stance_multipliers = {
    "none": 1.0,      # Обычная стойка
    "ctrl": 0.9,      # Присед уменьшает отдачу на 10%
    "shift": 1.2      # Спринт увеличивает отдачу на 20%
}

ctrl_multiplier = 0.9   # Уменьшаем множитель для CTRL на 10% (было 0.8)
shift_multiplier = 1.2  # Увеличиваем множитель для SHIFT на 20%

current_weapon = "M762"  # Текущее оружие, можно изменить в зависимости от ситуации
current_scope = "none"
current_grip = "none"
current_stock = "none"
current_muzzle = "none"
adjusted_down = 0  # Инициализация переменной

# Хранилище текущих обвесов и прицела
current_attachments = {
    "scope": "none",
    "grip": "none",
    "stock": "none",
    "muzzle": "none"
}

# Добавляем глобальную переменную для клавиши приседания
crouch_key = "ctrl_l"  # По умолчанию CTRL

def set_crouch_key(key):
    """
    Устанавливает клавишу для приседания
    """
    global crouch_key
    key_mapping = {
        "CTRL": "ctrl_l",
        "ALT": "alt_l",
        "C": "c"
    }
    crouch_key = key_mapping.get(key, "ctrl_l")

def check_crouch_pressed():
    """
    Проверяет нажатие выбранной клавиши приседания
    """
    if crouch_key == "ctrl_l":
        return win32api.GetKeyState(0xA2) < 0 or win32api.GetKeyState(0xA3) < 0
    elif crouch_key == "alt_l":
        return win32api.GetKeyState(0xA0) < 0 or win32api.GetKeyState(0xA1) < 0
    elif crouch_key == "c":
        return win32api.GetKeyState(0x43) < 0
    return False

def set_current_weapon(weapon_name):
    global current_weapon
    if (weapon_name in weapon_patterns):
        current_weapon = weapon_name
    else:
        current_weapon = "none"

def set_current_weapon_by_detected_name(gun_name_1):
    """
    Set current weapon with better error handling
    """
    global current_weapon
    # Проверяем все возможные варианты "none"
    if not gun_name_1 or gun_name_1.lower() in ['none', 'unknown', '未知']:
        current_weapon = "none"
        recalculate_recoil()  # Важно вызвать пересчет для обнуления
        return
        
    gun_name_1 = str(gun_name_1).lower()
    if gun_name_1 in weapon_patterns:
        current_weapon = gun_name_1
        recalculate_recoil()
    else:
        current_weapon = "none"

def select_preset_by_weapon_name(weapon_name):
    set_current_weapon(weapon_name)
    with open("log.txt", "a") as log_file:  # Запись в журнал
        log_file.write(f"Preset selected for weapon: {weapon_name}\n")

def set_current_attachments(scope, grip, stock, muzzle):
    """
    Установка обвесов с немедленным пересчетом отдачи
    """
    global current_scope, current_grip, current_stock, current_muzzle, current_attachments
    current_scope = scope
    current_grip = grip
    current_stock = stock
    current_muzzle = muzzle
    current_attachments = {
        "scope": scope,
        "grip": grip,
        "stock": stock,
        "muzzle": muzzle
    }
    recalculate_recoil()

def mouse_down():
    return win32api.GetKeyState(0x01) < 0

# Добавляем глобальную переменную для множителя прицелов
global_scope_multiplier = 35  # Default value = 1x

def set_scope_multiplier(value):
    """Sets global scope multiplier"""
    global global_scope_multiplier
    if not isinstance(value, (int, float)):
        value = 40
    if value <= 0:
        value = 1
    global_scope_multiplier = value
    print(f"Set new scope value: {value}")
    recalculate_recoil()

def calculate_total_multiplier(scope, grip, stock, muzzle, ctrl_pressed):
    """Calculate total recoil multiplier based on all attachments"""
    try:
        scope_mult = scope_multipliers.get(scope.lower().strip(), 1.0)
        grip_mult = grip_multipliers.get(grip.lower(), 1.0)
        stock_mult = stock_multipliers.get(stock.lower(), 1.0)
        muzzle_mult = muzzle_multipliers.get(muzzle.lower(), 1.0)
        return scope_mult * grip_mult * stock_mult * muzzle_mult
    except AttributeError:
        return 1.0

def get_base_recoil(gun_name):
    """
    Получить базовое значение отдачи для оружия
    """
    return weapon_patterns.get(gun_name.lower(), 0)

def recalculate_recoil():
    global adjusted_down
    if not current_weapon or current_weapon.lower() == "none":
        adjusted_down = 0
        return

    adjusted_down = calculate_final_adjusted_down(
        current_weapon,
        current_attachments["scope"],
        current_attachments["grip"],
        current_attachments["stock"],
        current_attachments["muzzle"],
        False,
        False
    )

def calculate_stance_multiplier(ctrl_pressed, shift_pressed):
    multiplier = 1.0
    if ctrl_pressed:
        multiplier *= 0.9
    if shift_pressed:
        multiplier *= 1.2
    return multiplier

# Добавляем глобальные переменные для чувствительности
global_ads_multiplier = 36  # ADS sensitivity (default 50 = 1x)
global_vertical_multiplier = 1.45  # Vertical multiplier (default 1.0 = 1x)

def set_ads_multiplier(value):
    """Sets ADS sensitivity multiplier"""
    global global_ads_multiplier
    if not isinstance(value, (int, float)):
        print(f"Invalid ADS value: {value}, using default 50")
        value = 50
    if value <= 0:
        value = 1
    global_ads_multiplier = value
    print(f"Set new ADS value: {value}")  # Debug
    recalculate_recoil()

def set_vertical_multiplier(value):
    """Sets vertical sensitivity multiplier"""
    global global_vertical_multiplier
    global_vertical_multiplier = value
    print(f"Set new vertical value: {value}")  # Debug
    recalculate_recoil()

def calculate_scope_multiplier(scope):
    """Convert scope name and get multiplier"""
    scope = scope.lower().strip()
    # Преобразуем альтернативные названия в стандартные
    conversion = {
        '2x': '2bei',
        '3x': '3bei', 
        '4x': '4bei',
        '6x': '6bei',
        '8x': '8bei',
        '15x': '15bei'
    }
    scope = conversion.get(scope, scope)
    
    try:
        # Загружаем пользовательские настройки множителей
        with open('./Config/config.json', 'r') as f:
            config = json.load(f)
            user_multipliers = config.get('sensitivity', {})
            
        # Если для прицела есть пользовательская настройка, используем её
        if scope in user_multipliers:
            value = float(user_multipliers[scope])
            print(f"Using custom scope multiplier for {scope}: {value}")
            return value
    except Exception as e:
        print(f"Error loading custom scope multiplier: {e}")
        
    # Если не удалось загрузить пользовательские настройки, используем дефолтные
    default_value = scope_multipliers.get(scope, 1.0)
    print(f"Using default scope multiplier for {scope}: {default_value}")
    return default_value

def calculate_final_adjusted_down(gun_name, scope, grip, stock, muzzle, ctrl_pressed, shift_pressed):
    """Calculate final vertical recoil with all multipliers"""
    if not gun_name or gun_name.lower() in ['none', 'unknown', '未知']:
        return 0
        
    base_recoil = weapon_patterns.get(gun_name.lower(), 0)
    if base_recoil == 0:
        return 0
    
    try:
        # Получаем множители обвесов
        grip_mult = grip_multipliers.get(grip.lower(), 1.0)
        stock_mult = stock_multipliers.get(stock.lower(), 1.0)
        muzzle_mult = muzzle_multipliers.get(muzzle.lower(), 1.0)
        
        # Получаем базовый множитель прицела
        scope = scope.lower().strip()
        base_scope_mult = scope_multipliers.get(scope, 1.0)
        
        # Список оптических прицелов
        optical_scopes = {
            '2bei', '3bei', '4bei', '6bei', '8bei', '15bei',
            '2x', '3x', '4x', '6x', '8x', '15x'
        }
        
        # Для оптических прицелов применяем множитель
        if scope in optical_scopes:
            if global_scope_multiplier == 40:
                ui_mult = 1.0
            elif global_scope_multiplier == 20:
                ui_mult = 2.0
            elif global_scope_multiplier == 80:
                ui_mult = 0.5
            else:
                ui_mult = 40.0 / global_scope_multiplier
                
            scope_mult = base_scope_mult * ui_mult
            print(f"Scope {scope}: base={base_scope_mult}x, UI={global_scope_multiplier}, mult={ui_mult}x, final={scope_mult:.2f}x")
        else:
            scope_mult = base_scope_mult
        
        # Применяем множители последовательно
        final_recoil = base_recoil
        final_recoil *= (grip_mult * stock_mult * muzzle_mult * scope_mult)
        final_recoil *= calculate_stance_multiplier(ctrl_pressed, shift_pressed)
        final_recoil *= (50.0 / global_ads_multiplier)
        final_recoil *= (1.0 / global_vertical_multiplier)
        
        print(f"Recoil calc: base={base_recoil:.2f}, final={final_recoil:.2f}")
            
    except Exception as e:
        print(f"Error in recoil calculation: {e}")
        return base_recoil

    return round(final_recoil, 2)

def calculate_recoil_with_state(recoil_value, gun_name, scope, grip, stock, muzzle, posture_state, shift_state):
    """
    Расчет отдачи с учетом только установленных модулей
    """
    # Получаем базовую отдачу
    base_recoil = recoil_value or weapon_patterns.get(gun_name.lower(), 0)
    if base_recoil == 0:
        return 0
    
    # Применяем обвесы
    final_recoil = base_recoil
    if grip and grip.lower() != "none":
        final_recoil *= grip_multipliers.get(grip.lower(), 1.0)
    if muzzle and muzzle.lower() != "none":
        final_recoil *= muzzle_multipliers.get(muzzle.lower(), 1.0)
    if stock and stock.lower() != "none":
        final_recoil *= stock_multipliers.get(stock.lower(), 1.0)
    if scope and scope.lower() != "none":
        final_recoil *= scope_multipliers.get(scope.lower(), 1.0)
    
    # Применяем модификаторы по отдельности
    if posture_state == "c":
        final_recoil *= 0.9  # Уменьшение на 10%
    if shift_state:
        final_recoil *= 1.2
    
    return round(final_recoil, 2)

# Remove redundant recoil calculations and only use calculate_final_adjusted_down
def recoil():
    global program_started
    while True:
        if mouse_down() and macro_enabled.is_set() and program_started:
            # Получаем базовую отдачу
            current_base_recoil = weapon_patterns.get(current_weapon.lower() if current_weapon else "none", 0)
            
            if current_base_recoil == 0:
                time.sleep(0.01)
                continue
                
            # Получаем состояние клавиш
            ctrl_pressed = check_crouch_pressed()
            shift_pressed = win32api.GetKeyState(0xA0) < 0 or win32api.GetKeyState(0xA1) < 0
            
            # Вычисляем финальную отдачу
            final_value = calculate_final_adjusted_down(
                current_weapon,
                current_attachments["scope"],
                current_attachments["grip"], 
                current_attachments["stock"],
                current_attachments["muzzle"],
                ctrl_pressed,
                shift_pressed
            )
            
            # Применяем движение мыши
            if final_value > 0:
                print(f"Moving mouse: {final_value}")
                win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, 0, int(final_value))
                
        time.sleep(0.01)

# Добавляем глобальную переменную
program_started = False

def start_program():
    """Called when Start button is pressed"""
    global program_started
    program_started = True
    print("Program started, macro can now be toggled")  # Debug

def toggle_macro():
    """Toggle macro state only if program is started
    This function is triggered by middle mouse button click (previously caps lock)"""
    global macro_enabled, program_started
    
    if not program_started:
        print("Cannot toggle macro - program not started")
        return False
        
    # Переключаем состояние макроса
    if macro_enabled.is_set():
        macro_enabled.clear()
        status = "Macro disabled"
    else:
        macro_enabled.set()
        status = "Macro enabled"
        recalculate_recoil()
        
    # Возвращаем и статус, и состояние для синхронизации UI
    return {
        "enabled": macro_enabled.is_set(),
        "status": status
    }

def toggle_disable_tab():
    """Toggle tab key functionality
    When disabled_tab is set (True), tab key functionality is disabled
    When disabled_tab is clear (False), tab key functionality is enabled (normal)"""
    global disabled_tab
    if disabled_tab.is_set():
        # Tab is currently disabled, enable it
        disabled_tab.clear()
        return {
            "enabled": disabled_tab.is_set(),  # Will be False
            "status": "Tab enabled"  # Correct status message
        }
    else:
        # Tab is currently enabled, disable it
        disabled_tab.set()
        return {
            "enabled": disabled_tab.is_set(),  # Will be True
            "status": "Tab disabled"  # Correct status message
        }

# def toggle_disable_tab():
#     global disable_tab

#     if disable_tab:
#         print("Tab is disabled")
#         disable_tab = False
#     else:
#         print("Tab is enabled")
#         disable_tab = True

def start_macro():
    threading.Thread(target=recoil, daemon=True).start()

def load_weapon_patterns():
    try:
        with open('./Config/weapon_patterns.json', 'r') as f:
            patterns = json.load(f)
            weapon_patterns.update(patterns)
    except FileNotFoundError:
        # Если файл не существует, сохраняем текущие паттерны
        with open('./Config/weapon_patterns.json', 'w') as f:
            json.dump(weapon_patterns, f, indent=4)

# Загружаем паттерны при импорте
load_weapon_patterns()

# Запуск макроса при импорте модуля
start_macro()
