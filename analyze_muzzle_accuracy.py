#!/usr/bin/env python3
"""
Comprehensive analysis tool for muzzle detection accuracy differences between slot 1 and slot 2.
This script identifies why slot 2 has lower accuracy than slot 1.
"""

import cv2
import numpy as np
import os
import asyncio
from resolution_setting import RESOLUTION_SETTINGS, GUNS_REOLUTION_SETTINGS
from recognition import MSS_Img, match_sift, template_match_fallback

class MuzzleAccuracyAnalyzer:
    def __init__(self, resolution='1920x1080'):
        self.resolution = resolution
        self.regions = RESOLUTION_SETTINGS[resolution]
        self.capture_region = GUNS_REOLUTION_SETTINGS[resolution]
        self.muzzle_templates = self._load_muzzle_templates()
        
    def _load_muzzle_templates(self):
        """Load all muzzle templates"""
        templates = {}
        muzzle_path = "_internal/data/firearms/Muzzle/"
        
        if os.path.exists(muzzle_path):
            for filename in os.listdir(muzzle_path):
                if filename.endswith('.png'):
                    template_path = os.path.join(muzzle_path, filename)
                    img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        templates[filename[:-4]] = img
        
        return templates
    
    def analyze_coordinate_precision(self):
        """Analyze coordinate precision between slot 1 and slot 2"""
        print("Analyzing coordinate precision...")
        print("=" * 60)
        
        muzzle_1 = self.regions['Muzzle_1']
        muzzle_2 = self.regions['Muzzle_2']
        
        print(f"Muzzle_1 coordinates: {muzzle_1}")
        print(f"Muzzle_2 coordinates: {muzzle_2}")
        
        # Calculate region properties
        m1_width = muzzle_1[2] - muzzle_1[0]
        m1_height = muzzle_1[3] - muzzle_1[1]
        m2_width = muzzle_2[2] - muzzle_2[0]
        m2_height = muzzle_2[3] - muzzle_2[1]
        
        print(f"Muzzle_1 size: {m1_width}x{m1_height}")
        print(f"Muzzle_2 size: {m2_width}x{m2_height}")
        
        # Check for size discrepancies
        width_diff = abs(m1_width - m2_width)
        height_diff = abs(m1_height - m2_height)
        
        if width_diff > 2 or height_diff > 2:
            print(f"⚠️  ISSUE: Significant size difference detected!")
            print(f"   Width difference: {width_diff} pixels")
            print(f"   Height difference: {height_diff} pixels")
            return False
        else:
            print("✓ Region sizes are consistent")
            return True
    
    def analyze_template_compatibility(self):
        """Analyze template size compatibility with ROI regions"""
        print("\nAnalyzing template compatibility...")
        print("=" * 60)
        
        muzzle_1 = self.regions['Muzzle_1']
        muzzle_2 = self.regions['Muzzle_2']
        
        roi1_width = muzzle_1[2] - muzzle_1[0]
        roi1_height = muzzle_1[3] - muzzle_1[1]
        roi2_width = muzzle_2[2] - muzzle_2[0]
        roi2_height = muzzle_2[3] - muzzle_2[1]
        
        print(f"ROI 1 size: {roi1_width}x{roi1_height}")
        print(f"ROI 2 size: {roi2_width}x{roi2_height}")
        
        compatibility_issues = []
        
        for template_name, template_img in self.muzzle_templates.items():
            t_height, t_width = template_img.shape
            
            # Check if template is significantly larger than ROI
            if t_width > roi1_width * 1.2 or t_height > roi1_height * 1.2:
                compatibility_issues.append(f"{template_name}: {t_width}x{t_height} too large for ROI 1")
            
            if t_width > roi2_width * 1.2 or t_height > roi2_height * 1.2:
                compatibility_issues.append(f"{template_name}: {t_width}x{t_height} too large for ROI 2")
        
        if compatibility_issues:
            print("⚠️  Template compatibility issues found:")
            for issue in compatibility_issues:
                print(f"   {issue}")
            return False
        else:
            print("✓ All templates are compatible with ROI sizes")
            return True
    
    def simulate_detection_accuracy(self):
        """Simulate detection accuracy using synthetic test data"""
        print("\nSimulating detection accuracy...")
        print("=" * 60)
        
        # Create synthetic ROI images with different characteristics
        test_scenarios = [
            ("high_contrast", self._create_high_contrast_roi),
            ("low_contrast", self._create_low_contrast_roi),
            ("noisy", self._create_noisy_roi),
            ("blurred", self._create_blurred_roi)
        ]
        
        results = {}
        
        for scenario_name, roi_generator in test_scenarios:
            print(f"\nTesting scenario: {scenario_name}")
            
            # Generate ROIs for both slots
            roi1 = roi_generator(slot=1)
            roi2 = roi_generator(slot=2)
            
            # Test against all templates
            slot1_scores = []
            slot2_scores = []
            
            for template_name, template_img in self.muzzle_templates.items():
                score1 = match_sift(roi1, template_img, "Muzzle")
                score2 = match_sift(roi2, template_img, "Muzzle")
                
                slot1_scores.append(score1)
                slot2_scores.append(score2)
            
            avg_score1 = np.mean(slot1_scores)
            avg_score2 = np.mean(slot2_scores)
            
            print(f"  Slot 1 average score: {avg_score1:.4f}")
            print(f"  Slot 2 average score: {avg_score2:.4f}")
            print(f"  Accuracy difference: {abs(avg_score1 - avg_score2):.4f}")
            
            results[scenario_name] = {
                'slot1_avg': avg_score1,
                'slot2_avg': avg_score2,
                'difference': abs(avg_score1 - avg_score2)
            }
        
        return results
    
    def _create_high_contrast_roi(self, slot):
        """Create high contrast test ROI"""
        if slot == 1:
            size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                   self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        else:
            size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                   self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        roi = np.zeros(size, dtype=np.uint8)
        roi[size[0]//4:3*size[0]//4, size[1]//4:3*size[1]//4] = 255
        return roi
    
    def _create_low_contrast_roi(self, slot):
        """Create low contrast test ROI"""
        if slot == 1:
            size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                   self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        else:
            size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                   self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        roi = np.full(size, 120, dtype=np.uint8)
        roi[size[0]//4:3*size[0]//4, size[1]//4:3*size[1]//4] = 140
        return roi
    
    def _create_noisy_roi(self, slot):
        """Create noisy test ROI"""
        if slot == 1:
            size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                   self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        else:
            size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                   self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        roi = np.random.randint(0, 255, size, dtype=np.uint8)
        return roi
    
    def _create_blurred_roi(self, slot):
        """Create blurred test ROI"""
        roi = self._create_high_contrast_roi(slot)
        return cv2.GaussianBlur(roi, (5, 5), 1.0)
    
    def analyze_preprocessing_differences(self):
        """Analyze if there are preprocessing differences affecting accuracy"""
        print("\nAnalyzing preprocessing differences...")
        print("=" * 60)
        
        # Check if the image capture process treats slot 1 and slot 2 differently
        issues_found = []
        
        # Verify coordinate calculations
        muzzle_1 = self.regions['Muzzle_1']
        muzzle_2 = self.regions['Muzzle_2']
        
        # Check for coordinate system issues
        if muzzle_1[0] < 0 or muzzle_1[1] < 0:
            issues_found.append("Muzzle_1 has negative coordinates")
        
        if muzzle_2[0] < 0 or muzzle_2[1] < 0:
            issues_found.append("Muzzle_2 has negative coordinates")
        
        # Check if regions extend beyond capture area
        cap_width, cap_height = self.capture_region[2], self.capture_region[3]
        
        if muzzle_1[2] > cap_width or muzzle_1[3] > cap_height:
            issues_found.append("Muzzle_1 extends beyond capture area")
        
        if muzzle_2[2] > cap_width or muzzle_2[3] > cap_height:
            issues_found.append("Muzzle_2 extends beyond capture area")
        
        if issues_found:
            print("⚠️  Preprocessing issues found:")
            for issue in issues_found:
                print(f"   {issue}")
            return False
        else:
            print("✓ No preprocessing issues detected")
            return True

def main():
    """Main analysis function"""
    print("MUZZLE DETECTION ACCURACY ANALYSIS")
    print("=" * 60)
    
    analyzer = MuzzleAccuracyAnalyzer()
    
    # Run all analyses
    coord_ok = analyzer.analyze_coordinate_precision()
    template_ok = analyzer.analyze_template_compatibility()
    preprocess_ok = analyzer.analyze_preprocessing_differences()
    
    # Run simulation
    simulation_results = analyzer.simulate_detection_accuracy()
    
    # Summary
    print("\n" + "=" * 60)
    print("ANALYSIS SUMMARY")
    print("=" * 60)
    
    issues = []
    if not coord_ok:
        issues.append("Coordinate precision issues")
    if not template_ok:
        issues.append("Template compatibility issues")
    if not preprocess_ok:
        issues.append("Preprocessing issues")
    
    # Check simulation results for significant differences
    for scenario, results in simulation_results.items():
        if results['difference'] > 0.05:  # 5% difference threshold
            issues.append(f"Significant accuracy difference in {scenario} scenario")
    
    if issues:
        print("⚠️  ISSUES IDENTIFIED:")
        for issue in issues:
            print(f"   • {issue}")
    else:
        print("✓ No major accuracy issues detected")
    
    print("\nRecommendations for fixing accuracy disparities:")
    print("1. Implement adaptive thresholding based on ROI characteristics")
    print("2. Add preprocessing normalization for both slots")
    print("3. Use multiple matching algorithms and combine results")
    print("4. Implement confidence score calibration")

if __name__ == "__main__":
    main()
