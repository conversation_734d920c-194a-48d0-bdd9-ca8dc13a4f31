# noinspection SpellCheckingInspection
gun_dict = {
    'M762': {
        'stand': 1,
        'squat': 0.80,
        'lie': 0.56,
        'guichs': [0, 0, 0],
        'guichn': [
            10, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2,
            0, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 4, 0, 3, 0, 4, 4, 1, 4, 4, 1, 3, 0, 4, 3,
            0, 4, 3, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 3, 1, 3, 0, 3, 3,
            0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 4, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 1, 4, 0, 4, 4, 0, 3, 4, 0, 0, 0, 3, 3,
            1, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [0, 0, 0],
        'guicvn': [
            10, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 1, 3, 0, 3, 5, 0, 3, 5, 0, 3, 0, 3,
            3, 0, 4, 0, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 1, 3, 0, 3, 4, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicns': [0, 0, 0],
        'guicnn': [
            5, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 4, 0, 3,
            3, 0, 3, 3, 1, 4, 0, 3, 4, 1, 4, 4, 0, 5, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 5,
            0, 4, 1, 4, 4, 0, 5, 4, 0, 4, 1, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 1, 4, 0, 4,
            4, 0, 4, 5, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 4, 4,
            0, 5, 0, 4, 4, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4, 0, 5,
            4, 0, 4, 4, 1, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 6, 4, 0, 5, 5, 0, 6, 0, 5, 5, 1, 5, 5, 0, 5, 0, 4, 5, 0, 4, 4,
            0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [
            10, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 5, 0, 4, 4, 0, 0, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 3, 3,
            0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 8, 0, 4, 3, 0, 3, 3, 0,
            4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 4, 0, 3, 4,
            0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 4, 0,
            3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 5, 0, 3, 0, 3, 3, 0, 3, 0, 4, 3,
            0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [0, 0, 0],
        'guifvn': [
            10, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 2,
            2, 1, 2, 0, 2, 3, 0, 4, 0, 0, 4, 3, 1, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 4, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 1, 3, 3,
            1, 3, 0, 3, 3, 1, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 0, 4, 1, 4, 0, 4, 4, 0, 4, 3, 0, 4, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 4, 3, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 4,
            0, 3, 4, 0, 3, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 5, 0, 4, 4, 0, 5, 0, 4, 3, 1, 3, 3, 1,
            3, 0, 4, 3, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 5, 4, 0, 4, 1, 4, 4, 1, 3, 3, 0, 3, 0, 3, 4,
            0, 4, 3, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 5, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 1, 3, 3, 0,
            4, 1, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [0, 0, 0],
        'guifnn': [
            5, 0, 3, 2, 0, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 6, 0, 4, 6, 0, 5, 0, 4, 4, 0,
            4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 6, 0, 4, 4, 1, 5,
            0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 0, 5, 0, 5, 5, 0, 6, 0, 5, 5, 0, 5, 0, 5, 5, 1, 5, 5, 0, 4, 1, 4, 5, 0,
            4, 5, 1, 5, 5, 0, 6, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 0, 4, 0, 4, 5, 0, 4,
            5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 5, 0, 4, 0,
            5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4, 0, 5, 4, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [
            5, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 3, 0, 3,
            0, 3, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 4,
            3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 4, 5, 0, 4, 0,
            5, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 5, 0, 4, 6, 0, 4, 0, 4, 4, 0, 5,
            0, 4, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 1, 4, 0, 6, 4, 0, 4, 0, 4, 4, 0,
            5, 4, 0, 5, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4,
            4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 6, 0, 4, 5, 0, 4, 4, 1, 4, 1,
            4, 4, 1, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 5, 0, 4, 5, 1, 5, 5, 1, 5, 1, 5, 5, 1, 4, 4, 0, 4, 1, 4, 4, 1, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 0, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 4, 5, 0, 4, 4, 0, 4, 1,
            4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 5, 0, 5, 0, 5, 5, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [0, 0, 0],
        'guinvn': [
            5, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0,
            3, 0, 3, 3, 1, 4, 3, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 5, 0, 4, 0, 4, 3,
            0, 4, 3, 0, 4, 0, 4, 0, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 0, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 1, 5, 4, 0, 4, 1, 4,
            4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            5, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 6, 5, 0,
            4, 5, 0, 4, 0, 4, 6, 0, 4, 1, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 1, 5, 5, 0, 5, 0, 0, 5, 0, 5, 6, 0, 5, 0, 5, 5,
            0, 5, 5, 0, 5, 5, 1, 5, 0, 5, 5, 0, 5, 5, 1, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 6, 5, 0, 7, 6, 0, 6, 0, 5, 6, 0,
            5, 0, 5, 5, 0, 6, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 6, 5, 0, 6, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 7, 5, 0, 5,
            0, 5, 5, 0, 5, 6, 0, 5, 6, 0, 1, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 7, 6, 0, 6, 5, 1, 5, 0,
            5, 5, 0, 5, 0, 6, 5, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 6, 1, 6, 0, 6, 6, 0, 6, 6, 0, 7, 0, 6, 6, 0, 5, 5, 0, 5,
            5, 0, 5, 0, 0, 5, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 5, 0, 5, 5, 0, 5, 0,
            5, 5, 0, 5, 0, 6, 5, 0, 6, 6, 0, 6, 0, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 6, 0, 5, 0, 5, 5, 0, 5, 5, 0,
            5, 0, 5, 6, 0, 5, 6, 0, 6, 0, 6, 6, 0, 6,
        ],
        'guicts': [0, 0, 0],
        'guictn': [
            5, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3,
            0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5, 4, 0, 6, 0, 4, 4, 1, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4,
        ],
        'guifts': [0, 0, 0],
        'guiftn': [
            5, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 5, 0, 4, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 5, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 6, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5,
        ],
        'guints': [0, 0, 0],
        'guintn': [
            5, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 5, 0, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0,
            5, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 6, 0, 5, 6, 0, 6, 6, 0, 5, 5, 0, 5, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 6, 0, 5, 6, 0, 6, 6, 0, 5, 0, 5, 5, 0, 5, 5,
            0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 5, 0, 6, 6, 0, 6, 5, 0, 5, 5, 0, 5, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 6, 6, 0, 6, 0, 5, 5, 0, 5, 5, 0, 5, 5,
            0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 6, 6, 0, 6, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 6,
        ],
    },

    'ACE32': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [
            1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 1, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 3, 0, 0, 2, 3, 0, 2, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2,
            1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 1, 2, 0, 3, 3, 0, 3, 4, 0, 3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 2,
            0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 4, 2, 0, 3, 0, 2,
            3, 0, 2, 2, 0, 2, 0, 2, 0, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 4, 0, 3, 2, 0, 2, 0, 2, 3,
            0, 2, 2, 0, 3, 1, 2, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 1, 2, 2, 0,
            2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 3,
            0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guichn': [
            1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            1, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 3, 2, 1, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 2, 0,
            2, 3, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 2, 0, 2,
            0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0,
            2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 1, 3, 3, 0, 3, 2, 0, 2,
            0, 3, 2, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 0, 0, 2, 0, 2, 2, 0,
            3, 2, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 3, 1, 3, 3, 0, 3, 0, 2, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 2, 3, 0, 2, 0,
            3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 2, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [
            1, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 0, 2, 0, 2, 0, 2, 2, 0, 1,
            1, 1, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 3,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 0, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 3, 0, 3, 3, 0, 2, 1, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 3, 1, 2, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 3, 0, 2, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2,
            3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 1, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 0,
            3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 4,
            2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 0, 0, 0, 0, 0, 1, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvn': [
            1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 3, 0, 3,
            2, 0, 2, 0, 2, 3, 0, 2, 2, 1, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 0, 0,
            2, 2, 0, 2, 2, 1, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2,
            2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0,
            3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3,
            2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0,
            3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicns': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3,
            0, 3, 2, 0, 2, 3, 0, 2, 0, 2, 3, 0, 3, 2, 0, 3, 0, 3, 4, 0, 3, 0, 3, 0, 0, 4, 3, 0, 3, 1, 3, 3, 1, 3, 3, 0,
            4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 4, 0, 4, 4, 0, 4, 0, 4, 1, 0, 3, 3, 1, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 4, 4, 1, 4, 0, 4, 4, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 5, 3, 0, 3, 0, 3, 3, 0,
            3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 3, 3, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 1, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            4, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            1, 0, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicnn': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3,
            0, 3, 2, 0, 2, 3, 0, 2, 0, 2, 3, 0, 3, 2, 0, 3, 0, 3, 4, 0, 3, 0, 3, 0, 0, 4, 3, 0, 3, 1, 3, 3, 1, 3, 3, 0,
            4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 4, 0, 4, 4, 0, 4, 0, 4, 1, 0, 3, 3, 1, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 4, 4, 1, 4, 0, 4, 4, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 5, 3, 0, 3, 0, 3, 3, 0,
            3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 3, 3, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 1, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            4, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            1, 0, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [
            1, 2, 0, 1, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3,
            0, 2, 2, 1, 2, 2, 0, 3, 0, 2, 2, 1, 2, 2, 0, 2, 1, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 2, 0,
            2, 2, 0, 2, 0, 3, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 1, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 3, 0, 2,
            0, 3, 2, 0, 4, 2, 1, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3,
            0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 3, 0,
            2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 1, 3, 0, 3, 2, 0, 2, 3, 1, 2,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 2, 0, 0, 0, 0, 0, 1,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhn': [
            1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 1, 3, 2, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 2, 0, 2,
            2, 1, 3, 0, 2, 3, 0, 3, 3, 1, 3, 0, 3, 3, 1, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0,
            4, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 1, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0,
            4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 4, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 4, 3, 0, 5, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 5, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 3, 5, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [
            1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 3, 0, 2, 3, 0, 3, 2, 0,
            2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2,
            0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 1,
            3, 0, 3, 3, 1, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 1, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvn': [
            1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0,
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3,
            0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 4, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 4, 0, 3, 0, 3, 4, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 1, 3, 3, 0,
            3, 1, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 1, 3, 3, 3, 0, 0, 0, 0, 0, 1,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [
            1, 0, 1, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 4,
            0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 5,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4,
            1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifnn': [
            2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 1, 5, 5, 0, 4, 4, 0, 4,
            0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 4,
            1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 0, 5, 0, 5, 4, 0, 4, 0, 4, 4, 0,
            5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3,
            0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 4, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 1, 4, 4, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 3, 0, 3, 3, 4, 0, 3, 4, 4, 0, 5, 0, 4, 3, 0, 4, 3, 0, 3,
            1, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 1, 3, 4, 0, 3, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 3, 3, 1, 3, 3, 0,
            4, 3, 0, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 4, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 3, 1, 3, 3, 1, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 5, 0, 3, 4, 1, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 1, 3, 0, 3, 3, 1, 3, 3, 0, 3,
            0, 4, 3, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 1, 3, 0, 0, 0, 0, 1, 0, 0,
        ],
        'guinhn': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 0, 0, 2, 3, 0, 3, 3,
            0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 4, 3, 0, 0, 0, 4, 5, 0, 4, 4, 0, 4, 0, 3,
            3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 1, 3, 3,
            1, 3, 0, 4, 3, 0, 5, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 4, 0, 3, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 5, 4, 0, 5, 4, 0, 4, 1, 4, 3, 0, 4, 3,
            0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 3, 0, 4, 0, 3,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 4, 1, 3, 4,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 0, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [
            1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3,
            0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3,
            0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 5, 3, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 5, 0, 4, 1, 4, 3, 1, 3, 3, 0,
            3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 3, 0, 4, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 1, 4, 1, 3, 3, 0, 3, 4, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 0, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 0, 0, 0, 0, 0,
        ],
        'guinvn': [
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 4, 0, 3, 5, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 5, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 0, 3, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 4,
            3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 0, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 3, 0, 3, 3,
            0, 3, 0, 4, 3, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 1, 4,
            4, 1, 4, 4, 1, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 4, 3,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 5, 0, 3,
            4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 3, 4, 0, 3, 4,
            0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 5, 0, 0, 0, 0,
        ],
        'guinns': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 3, 0,
            3, 0, 3, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 1, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 4, 1, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 0, 4,
            4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 6, 5, 0, 5, 0, 5, 5, 0, 5, 1, 4, 4, 0, 4, 4,
            0, 5, 0, 4, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 5, 5, 0, 5, 5, 0, 6, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 5, 0, 4, 0, 6, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 5, 4, 1, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 1, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0,
        ],
        'guinnn': [
            2, 2, 1, 2, 0, 3, 2, 0, 3, 0, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0,
            5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 6, 5, 0, 4,
            0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0,
            0, 4, 0, 5, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4,
            4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0,
            5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 6, 5, 0, 5, 0, 5, 5, 0, 5,
            0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 5, 5, 0, 5, 0, 5, 5, 1,
            5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 5, 0, 6, 5, 0, 5, 5, 0, 5,
            0, 5, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 0, 6, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [
            1, 1, 1, 1, 2, 1, 2, 1, 1, 2, 0, 3, 3, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 1, 3, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 1, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 0, 2,
            2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 0, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 4, -1, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 3,
        ],
        'guictn': [
            1, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 3, 2, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2,
            2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 3, 0, 2, 3, 0, 2, 3, 0, 4, 0, 2, 3, 0, 2,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, -1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 2, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0,
            4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 3, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 3, 0, 2, 0, 3, 3, -1,
            4, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 4, 0, 4, 3, 0, 4, 4, -1, 4, -1, 3, 4, -1, 4, 4, 0, 4, 0, 3, 3, -1, 3, 3,
            -1, 4, -1, 3, 4, 0, 4, 4, -1, 4, -1, 4, 4, 0, 4, 4, -1, 4, 0, 4, 3, 0, 4, 4, 0, 4, -1, 4, 4, 0, 3, 3,
        ],
        'guifts': [
            1, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 4, 0, 3, 0, 3,
            3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 0,
            3, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 0, 3,
        ],
        'guiftn': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 3, 4,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 1, 3, 0, 1, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, -1, 3, -1, 3, 3, 0, 2, -1, 4, 3, 0, 4, 3, 0, 2, 3, -1,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3,
            3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3,
            0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
        ],
        'guints': [
            2, 0, 2, 1, 2, 2, 0, 2, 3, 0, 3, 0, 2, 3, 0, 2, 2, 1, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 2, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3,
            3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 3, 4, 0, 4, 3, 0, 4, 0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 3, 0,
            3, 4, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 4, 4, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 5, 0, 4, 1, 4, 4, 0, 5, 4, 0, 6, 0, 5, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 4, 4,
        ],
        'guintn': [
            2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 1, 2, 2, 0, 3, 0, 2, 2, 0, 3, 0, 0, 2, 0, 3, 2, 0, 2, 3, 0, 2, 0, 4, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 4, -1, 4, 3, 0, 4, 0, 4, 4, 0, 4, 3, 0, 4, 0,
            3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 4, -1, 3, 0, 3, 5, -1, 4, -1, 5, 4, 0, 4, 4, 0,
            4, -1, 4, 3, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, -1, 4, 3, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 4, 0,
            4, 0, 4, 4, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 3, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 4, 0, 5, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, -1, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 5, 4, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4,
        ],
    },

    'AKM': {
        'stand': 1,
        'squat': 0.76,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [2, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
                   0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
                   3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0,
                   3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0,
                   3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3,
                   1, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 3, 0, 3,
                   3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 4, 0, 4, 4, 0,
                   5, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
                   4, 3, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
                   0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
                   3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
                   3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0,
                   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                   ],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 3, 0, 2, 3, 0, 2, 1,
                   3, 2, 0, 5, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
                   0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
                   3, 0, 3, 1, 3, 4, 0, 3, 4, 0, 4, 0, 5, 4, 0, 4, 3, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0,
                   3, 0, 3, 3, 0, 3, 4, 0, 4, 0, 3, 4, 1, 4, 4, 0, 5, 0, 4, 5, 0, 4, 3, 0, 4, 0, 3, 3, 0, 3, 3, 1, 3, 0,
                   3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 5, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 4,
                   0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 1, 3, 0, 4, 4, 0, 3, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4,
                   3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 1, 3, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0,
                   4, 0, 4, 4, 0, 5, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 4, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0,
                   4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 3, 0, 3, 1, 3, 3, 1, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 4, 1, 3, 4,
                   0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
                   3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                   ],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 3, 2, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3,
                   0, 3, 0, 3, 4, 0, 3, 2, 0, 2, 0, 3, 2, 0, 4, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
                   0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4,
                   5, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 1, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0,
                   4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 5, 0, 4, 6, 0, 5, 4, 0, 4, 0, 4, 5, 0, 4, 4,
                   0, 5, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 5, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 1, 4, 4, 0, 4,
                   0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 5, 5, 0, 4, 5, 0, 5, 1, 5,
                   5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0,
                   5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4,
                   0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 6, 0, 5, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5,
                   0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 6, 0, 4, 6, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4,
                   4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 6, 0, 4, 0, 0, 0, 0, 0, 0, 0,
                   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                   ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'GROZA': {
        'stand': 1,
        'squat': 0.67,
        'lie': 0.43,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 1, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 5, 0, 4, 0, 5, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0,
            4, 4, 0, 4, 4, 0, 5, 0, 3, 4, 1, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5,
            4, 1, 4, 0, 3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1,
            3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 3, 1, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 1, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 5, 0, 4, 0, 5, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0,
            4, 4, 0, 4, 4, 0, 5, 0, 3, 4, 1, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5,
            4, 1, 4, 0, 3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1,
            3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 3, 1, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0,
            4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 1, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 5, 0, 4, 0, 5, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0,
            4, 4, 0, 4, 4, 0, 5, 0, 3, 4, 1, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5,
            4, 1, 4, 0, 3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1,
            3, 4, 0, 3, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 3, 1, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MG3': {
        'stand': 1,
        'squat': 0.35,
        'lie': 0.25,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 2, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 2, 1, 1, 2, 1, 1, 2, 2, 2,
            2, 3, 2, 3, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 2, 1, 1, 1, 2, 1, 1, 2, 2, 1, 2, 2,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1,
            0, 1, 1, 0, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1,
            1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 1, 2, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0,
            1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1,
            1, 1, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 1,
            1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 2, 1, 1,
            1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 2, 0, 2, 1, 1, 1, 0, 1, 1, 0,
            1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1,
            1, 0, 2, 1, 1, 2, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 1, 0, 1, 0, 1, 1, 1, 1, 2, 1, 1, 1, 0, 1, 0, 0, 1, 0, 0, 0,
            0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 2, 1, 1,
            1, 2, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 2, 0, 2, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1,
            0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'DP28': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'M416': {
        'stand': 1,
        'squat': 0.83,
        'lie': 0.55,
        'guichs': [
            1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2,
            0, 2, 2, 0, 3, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 3, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 3, 0, 2, 1, 3, 3,
            0, 4, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 3, 2, 0,
            3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2,
            0, 4, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            4, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2,
            1, 2, 3, 0, 2, 0, 4, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            1, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guichn': [
            1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            3, 0, 2, 0, 4, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 3, 0, 2, 3, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 3, 0, 2, 0,
            3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 1, 2, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3,
            2, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            3, 2, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 4, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 4, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 3, 1, 0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 2, 2, 1, 2,
            2, 0, 3, 1, 2, 2, 0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 3, 3, 0, 2, 1,
            3, 3, 0, 3, 3, 0, 4, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2,
            4, 0, 3, 0, 3, 3, 1, 3, 3, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            2, 3, 1, 3, 0, 3, 3, 0, 4, 3, 0, 3, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2,
            3, 0, 2, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 3, 0, 2, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 1, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0,
        ],
        'guicvn': [
            1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 3, 3, 0, 4, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0,
            3, 3, 0, 3, 0, 3, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3,
            0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 4, 0, 2,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            3, 2, 1, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2,
            1, 2, 3, 0, 2, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0,
        ],
        'guicns': [
            2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 3, 2, 0, 2, 1, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 4, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 2, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3,
            1, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0,
            3, 3, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 6, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 4, 3, 0, 6, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 5, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 1, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0,
        ],
        'guicnn': [
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0,
            2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 1, 3, 3, 1, 3,
            3, 1, 3, 0, 3, 2, 0, 2, 2, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            4, 0, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3,
            3, 0, 3, 0, 3, 5, 0, 3, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 5, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 5, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [
            2, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2,
            1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            3, 0, 2, 0, 4, 2, 0, 2, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 3, 0, 2, 3, 0, 4, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 1, 3, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 4, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 3, 3, 1, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0,
            2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhn': [
            2, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2,
            1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            3, 0, 2, 0, 4, 2, 0, 2, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 3, 0, 2, 3, 0, 4, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 1, 3, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 4, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 3, 3, 1, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0,
            2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [
            1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 1, 2, 3, 0,
            3, 4, 0, 3, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 3, 2, 0, 3,
            0, 3, 4, 0, 4, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 1, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 1, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 3, 0, 2, 0, 3, 3, 0, 3, 4, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 1, 2, 0, 2, 1, 2, 3, 0, 2, 3, 0, 3, 0,
            4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 2, 0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 0, 4,
            3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvn': [
            1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 1, 2, 3, 0,
            3, 4, 0, 3, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 3, 2, 0, 3,
            0, 3, 4, 0, 4, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 1, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 1, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 3, 0, 2, 0, 3, 3, 0, 3, 4, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 1, 2, 0, 2, 1, 2, 3, 0, 2, 3, 0, 3, 0,
            4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 2, 0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 0, 4,
            3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 4, 0,
            3, 4, 0, 3, 0, 3, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0,
            4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 4,
            0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0,
            3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 3, 0, 3, 3, 1, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0,
        ],
        'guifnn': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 4, 0,
            3, 4, 0, 3, 0, 3, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0,
            4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 4,
            0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0,
            3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 3, 0, 3, 3, 1, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0,
        ],
        'guinhs': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 2, 0, 2, 1, 2, 3, 0, 2,
            2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0,
            3, 3, 0, 4, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 4, 5, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 5, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhn': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 2, 0, 2, 1, 2, 3, 0, 2,
            2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0,
            3, 3, 0, 4, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 4, 5, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 5, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [
            1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 1, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0,
            3, 3, 0, 2, 0, 2, 3, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 4, 0, 3, 5, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 1, 3, 3, 0, 4, 3, 0, 3, 5, 0, 3, 0, 6, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvn': [
            1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 1, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0,
            3, 3, 0, 2, 0, 2, 3, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 4, 0, 3, 5, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 1, 3, 3, 0, 4, 3, 0, 3, 5, 0, 3, 0, 6, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [
            2, 0, 3, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 0, 3, 2, 0, 3, 3, 1, 3, 0, 4, 3,
            0, 4, 3, 0, 2, 0, 3, 3, 0, 2, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0,
            3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 5, 0, 4, 4,
            0, 4, 4, 0, 5, 0, 4, 4, 0, 3, 4, 1, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 1, 4, 4,
            0, 5, 4, 0, 5, 4, 0, 5, 0, 6, 5, 0, 4, 5, 0, 4, 0, 4, 4, 1, 4, 0, 4, 4, 1, 4, 4, 1, 4, 0, 4, 4, 0, 4, 5, 0,
            4, 1, 4, 4, 0, 4, 5, 1, 4, 5, 0, 5, 1, 5, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 5, 4,
            0, 4, 4, 0, 4, 1, 4, 4, 0, 5, 4, 0, 5, 6, 0, 5, 0, 4, 5, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4,
            0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
        ],
        'guinnn': [
            2, 0, 3, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 0, 3, 2, 0, 3, 3, 1, 3, 0, 4, 3,
            0, 4, 3, 0, 2, 0, 3, 3, 0, 2, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0,
            3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 5, 0, 4, 4,
            0, 4, 4, 0, 5, 0, 4, 4, 0, 3, 4, 1, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 1, 4, 4,
            0, 5, 4, 0, 5, 4, 0, 5, 0, 6, 5, 0, 4, 5, 0, 4, 0, 4, 4, 1, 4, 0, 4, 4, 1, 4, 4, 1, 4, 0, 4, 4, 0, 4, 5, 0,
            4, 1, 4, 4, 0, 4, 5, 1, 4, 5, 0, 5, 1, 5, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 5, 4,
            0, 4, 4, 0, 4, 1, 4, 4, 0, 5, 4, 0, 5, 6, 0, 5, 0, 4, 5, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4,
            0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [
            1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 0, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            4, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 1, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
        ],
        'guictn': [
            1, 1, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, -1, 3, 3, 0, 3, 0, 3, 2, 0, 3, 3, -1, 3, 0, 3, 3, 0, 2, 0, 0, 0, 0, -1,
        ],
        'guifts': [
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            3, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, -1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            2, 0, 3, 0, 3, 3, -1, 3, 4, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3,
            3, 0, 3, 0, 2, 3, 0, 3, 3, -1, 3, 0, 4, 3, 0, 5, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1, 3, 0, 4, 3, -1, 4, 4, 0, 3, -1, 3, 3, 0, 3, 2, 0, 3, 3, 0, 3, -1, 3, 3,
            -1, 3, 3, -1, 3, 0, 3, 2, 0, 3, 0, 3, 3, -1, 3, 3, 0, 4, 2, 0, 4, 0, 4, 0, 0, 0, -1,
        ],
        'guiftn': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 3,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 0, 4, 3, 0, 3, 3, 0,
            3, 0, 3, 4, 0, 3, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 2, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4,
        ],
        'guints': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3,
            0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3,
        ],
        'guintn': [
            2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4,
            0, 3, 0, 4, 4, 0, 4, 0, 3, 3, 0, 2, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, -1, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 4, 4, -1, 4, 3, 0, 3, -1, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            0, 4, 4, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, -1,
        ],
    },

    'AUG': {
        'stand': 1,
        'squat': 0.80,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [
            5, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 2, 1, 2, 0, 0, 2, 2, 0,
            3, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 1, 3, 4,
            0, 3, 4, 0, 4, 0, 4, 0, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            4, 0, 3, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 3, 0, 4,
            0, 4, 4, 0, 4, 0, 4, 5, 0, 5, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 4, 5, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 3, 1, 4, 0, 4, 4, 0, 4, 1, 4, 4,
            0, 4, 4, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 5, 4, 0, 4, 0, 4, 5, 0, 5, 4, 0, 4, 3, 0, 4,
            0, 4, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [0, 0, 0],
        'guicvn': [
            5, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 0, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 5, 0, 4, 3, 0, 3, 0, 4, 3, 0, 3, 3, 1, 3, 0, 4, 3, 0,
            3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 4, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 4, 0, 4, 3, 0,
            3, 3, 0, 3, 1, 3, 4, 0, 3, 4, 0, 4, 0, 4, 3, 0, 3, 4, 0, 3, 0, 3, 4, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 4, 0, 4, 3, 0, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicns': [0, 0, 0],
        'guicnn': [
            5, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 2, 3,
            0, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 5, 4,
            0, 4, 0, 4, 4, 0, 5, 4, 1, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 5, 0, 4, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4,
            0, 1, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5,
            5, 0, 4, 0, 5, 5, 0, 5, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5,
            0, 5, 0, 5, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5,
            4, 0, 4, 5, 0, 4, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 6, 0, 5, 4, 0, 4, 4,
            0, 4, 1, 4, 4, 1, 4, 0, 4, 5, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [
            5, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 1, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 4, 3, 0, 3, 1, 2, 2, 1, 2, 2, 0, 3, 0,
            2, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 4, 3, 0, 5, 3, 0, 4, 0, 5, 4, 0, 4,
            5, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 1, 3, 0, 4, 3, 0, 4, 5, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 3, 3, 0, 3, 3, 0, 3, 1, 4, 3, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4,
            4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 5, 0,
            4, 4, 0, 4, 4, 1, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 4, 3, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 1, 4,
            4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 4, 0, 4, 3, 0, 5, 4, 4, 0, 4, 4, 1, 4, 4, 1, 4, 0, 4, 4, 1, 4, 4, 0, 5, 3, 1,
            3, 4, 0, 4, 0, 3, 4, 0, 3, 4, 1, 4, 1, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 5, 3, 0, 3, 4, 0, 3,
            0, 3, 4, 0, 3, 5, 0, 4, 0, 4, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [0, 0, 0],
        'guifvn': [
            5, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2,
            2, 1, 3, 0, 2, 3, 0, 4, 3, 0, 3, 1, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 4, 3, 0, 4, 0,
            4, 5, 0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 3, 0, 3, 3, 0, 0, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 5, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 3, 0, 3, 1, 3, 3, 0, 4, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 3,
            3, 1, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 5, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 3, 0, 3, 4, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [0, 0, 0],
        'guifnn': [
            5, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5,
            0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 1,
            5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0,
            5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 1, 0, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5,
            5, 0, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 6, 5, 0, 5, 0, 5, 5, 0, 4, 5, 0, 4,
            0, 0, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [
            5, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 4, 0, 3, 5, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 4, 1, 4, 0, 4, 4, 0, 5, 0, 4, 6, 0, 5,
            5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 5, 0, 4, 5,
            0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 5, 0, 4, 5, 0, 5, 0, 5,
            5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5,
            0, 6, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 1, 5, 0, 5, 5, 0, 5, 5, 0,
            5, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 6, 5, 0, 5, 0, 5, 5, 0, 4, 4,
            0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 4, 0,
            4, 0, 4, 4, 0, 0, 4, 0, 5, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [0, 0, 0],
        'guinvn': [
            5, 2, 0, 2, 0, 0, 2, 0, 2, 2, 0, 0, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 2, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 0, 4, 0, 4, 0, 5, 4, 0, 4,
            4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0,
            0, 5, 0, 5, 5, 0, 4, 0, 5, 4, 0, 4, 0, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5,
            5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 0, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 6, 0, 4, 0,
            4, 4, 0, 4, 4, 1, 4, 0, 5, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            5, 0, 2, 0, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 6, 5, 0, 5, 0, 6, 5, 0, 5, 6, 0, 5, 0,
            6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 5, 5, 0, 5, 0, 6, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 6, 5, 0, 6, 0, 6, 6, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 6, 6, 0, 6, 0,
            6, 7, 0, 6, 6, 0, 6, 6, 0, 6, 0, 5, 5, 0, 5, 0, 6, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 6, 6, 0, 6, 6, 0, 6, 0, 7,
            6, 0, 6, 0, 6, 7, 0, 5, 5, 0, 5, 5, 0, 5, 1, 5, 5, 1, 5, 0, 6, 5, 0, 6, 6, 0, 6, 6, 0, 6, 1, 6, 6, 0, 6, 1,
            6, 6, 0, 5, 5, 1, 5, 5, 1, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 5,
            5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 6, 6, 0, 6, 6, 0, 6, 0, 6, 6, 0, 6, 6, 0, 6, 0, 5, 5, 0, 5, 0,
            5, 5, 0, 5, 5, 0, 5, 0, 6, 5, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [
            3, 1, 0, 1, 1, 0, 2, 1, 2, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 1, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 2, 0, 3, 0, 2, 3, 0, 2, 0,
            3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 1, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4,
            4, 0, 4, 4, 0, 4, 1, 4, 5, 0, 3, 0, 3, 4, 0, 3, 3, 0, 4, 4, 0, 3, 0, 4, 5, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4,
            0, 4, 0, 4, 5, 0, 3, 3, 0, 3, 0, 4, 3, 0, 1, 4, 1, 3, 0, 4, 4, 1, 4, 4, 1, 4, 0, 5, 4, 0, 4, 4, 0, 4, 1, 4,
            4, 1, 3, 3, 0, 3, 1, 3, 3, 0, 4, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 1, 4, 4, 0, 4, 3,
            0, 3, 1, 3, 3, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 1, 3, 0, 3,
            3, 0, 4, 4, 0, 3, 0, 4, 4,
        ],
        'guifts': [0, 0, 0],
        'guiftn': [
            3, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3,
            2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 5, 4, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 5, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5,
            4, 0, 4, 0, 4, 4, 0, 4, 6, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 5, 5, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 5, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4,
            5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 6, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0, 1,
        ],
        'guints': [0, 0, 0],
        'guintn': [
            3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, -1, 3, 4, 0, 3, 4, 0, 3, 0, 4, 4, -1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5,
            6, 0, 6, 5, 0, 6, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5,
            0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5,
            5, 1, 5, 5, 0, 5, 0, 5, 6, 0, 5, 5, 0, 4, 1, 4, 4, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 6, 5, 0, 5, 5,
            0, 6, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 5, 5, 0, 4, 1, 5, 5, 0, 6, 5, 0, 5, 5, 1, 5, 0, 5, 5, 0, 6, 0, 5,
            5, 0, 5, 5, 0, 4, 1, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 6, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5,
            0, 4, 0, 4, 5, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 5, 5, 0, 4, 0, 4,
            4, 0, 4, 5, 0, 4, 0, 5, 5, 1, 5, 0, 0, 0, 0, 0, 0, 0, 1,
        ],
    },

    'G36C': {
        'stand': 1,
        'squat': 0.83,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [
            1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 0, 3, 0, 3,
            0, 3, 3, 0, 3, 0, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [0, 0, 0],
        'guicvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 1, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 3, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 1, 3,
            2, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 3, 2, 0, 3, 2, 0, 2, 0, 3, 2, 0, 2, 3, 0, 2, 0,
            4, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 1, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3,
            4, 0, 0, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicns': [0, 0, 0],
        'guicnn': [
            1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 4, 2, 0, 2, 0, 2, 3, 0,
            2, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 1, 4, 0, 4, 4, 0, 0, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 4, 3, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0,
            3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [
            1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2,
            0, 3, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 4, 0,
            2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 2, 2, 0, 3, 0, 2, 2, 1, 2, 2, 0, 3, 0, 3, 2, 0, 3,
            0, 3, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3, 2, 0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 0, 3, 4, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 0, 3, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [0, 0, 0],
        'guifvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 1, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 1, 3, 0,
            3, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [0, 0, 0],
        'guifnn': [
            1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 4, 0, 3, 1, 4, 4, 0,
            4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 3,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 4,
            4, 0, 5, 0, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 1, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5,
            4, 0, 4, 0, 5, 4, 0, 4, 0, 5, 3, 0, 4, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [
            1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 4, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [0, 0, 0],
        'guinvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0,
            3, 3, 0, 4, 3, 1, 4, 0, 4, 4, 0, 4, 3, 1, 3, 3, 0, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3,
            4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 5, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, -1, 5, 4, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 0, 4, 0, 4, 0, 4, 4, 0, 4,
            0, 5, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0,
            4, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 5, -1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [
            1, 0, 1, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, -1, 2, 1, 0, 2, 1, 0, 3, -1, 2, 2, 0, 2, 3,
            0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 1, 0, 3, -1, 2,
            3, 0, 2, 3, 0, 3, 3, -1, 3, 0, 2, 3, 0, 2, 0, 3, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, -1, 3, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2,
            0, 3, 3, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3, -1, 3, 0, 3, 2, 0, 3, 3, 0,
            2, 0, 3, 3, 0, 2, 3, 0, 3, 3, 0, 3, -1, 2, 3, 0, 2, 0, 3,
        ],
        'guifts': [0, 0, 0],
        'guiftn': [
            1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 2, 3, 0, 3, 0, 3, 3, -1, 3, 3, -1, 3, -1, 3, 3, 0,
            4, 3, 0, 4, 0, 4, 4, -1, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1, 3, 4, 0,
            3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, -1,
        ],
        'guints': [0, 0, 0],
        'guintn': [
            1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1, 3, 3, 0, 3, 0, 3, 2, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 4, 0, 3, 4, 0, 4, 0, 3, 2, 0, 3, 3, 0, 3,
            0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 4, 0, 3, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, -1, 4, 0, 5, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 4, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0,
            4, 4, -1, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 4, 4, 0, 4, 2, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 0, 4,
            4, 0, 3, 4, 0, 4, 0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 2, 0, 4, 4, 0, 4, 3, 0, 4, 0, 4, 4, 0, -1, 0, 4, 4, 0, 4, 4,
            0, 4, -1, 0, 4, 0, 4, 4, -1, 4, 4, 0, 4, 0, 3, 4, 0, 4, 3, 0, 4, 0, 0, -1,
        ],
    },

    'SCAR': {
        'stand': 1,
        'squat': 0.83,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [
            1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 1, 1, 0, 1, 0, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 1, 2,
            2, 0, 2, 2, 1, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 3,
            0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 3, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [0, 0, 0],
        'guicvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2,
            0, 2, 3, 0, 2, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 3, 2, 0, 3, 2, 0, 2, 0,
            2, 2, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2,
            3, 0, 2, 1, 2, 2, 0, 3, 2, 0, 4, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 2, 1, 2, 0,
            2, 2, 0, 2, 2, 1, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicns': [0, 0, 0],
        'guicnn': [
            1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 3, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2,
            1, 2, 0, 2, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 3,
            1, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 2, 1, 2, 0, 2, 2, 1, 2, 2, 0, 2, 4, 0, 2, 0, 4, 3, 0, 4, 0, 3,
            3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 2, 2, 2, 0, 2, 0, 3, 2, 0, 2, 0, 2, 4, 0, 2, 3, 0, 3, 0, 3, 4, 0, 3, 3,
            0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 3, 0, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 3, 2, 0, 3, 3, 0, 3, 3, 1,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3,
            3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            1, 0, 1, 0, 3, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
            2, 0, 2, 0, 4, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 3, 0, 2, 0,
            3, 3, 0, 4, 3, 0, 3, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3,
            2, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 3, 2, 0, 3, 2, 0, 3, 1, 3, 3, 0, 4, 3, 0, 3, 0, 3, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [0, 0, 0],
        'guifvn': [
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 3, 2,
            0, 2, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 2, 0, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            3, 2, 0, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 0, 2, 2,
            0, 2, 2, 0, 4, 2, 0, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 3, 0,
            2, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 1, 2, 2, 1, 2, 2, 0, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0,
            2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0,
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 1, 2, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 2, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0,
            1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [0, 0, 0],
        'guifnn': [
            1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0,
            3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 3, 2, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0,
            3, 3, 0, 4, 0, 3, 3, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 2, 0, 2, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 2, 2, 1, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 1,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [
            1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 4, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 0, 4, 0, 3, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [0, 0, 0],
        'guinvn': [
            1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3,
            2, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 2, 0, 3, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3,
            4, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 4, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 1, 2, 0, 2, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 3,
            2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0,
            3, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 5, 3, 0, 5, 0, 4, 4, 0, 5, 4,
            0, 4, 0, 4, 5, 0, 4, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 1, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 5, 0, 3, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4,
            0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 3, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3,
            3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 3,
            2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2,
            2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
        ],
        'guifts': [0, 0, 0],
        'guiftn': [
            1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 1, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 1, 2, 0, 2, 2, 0, 2, 0, 1, 2, 0, 3, 2, 0,
            3, -1, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2,
        ],
        'guints': [0, 0, 0],
        'guintn': [
            1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 4, 0, 3, 4, 0, 4, 0, 5, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1,
            3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 0, 3, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 0, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 3, 0, 3,
        ],
    },

    'QBZ': {
        'stand': 1,
        'squat': 0.83,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [
            1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2,
            0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            3, 2, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1,
            2, 3, 0, 2, 0, 4, 3, 1, 3, 3, 0, 4, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 3, 2, 0, 3, 1, 3, 3, 0, 4, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0,
            2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 4, 0, 4, 0, 3, 2, 1, 2, 1, 2, 3, 0, 2, 2, 1, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 2, 0, 3, 0, 2, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicvs': [0, 0, 0],
        'guicvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 2,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            3, 0, 2, 0, 3, 4, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicns': [0, 0, 0],
        'guicnn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 0, 3, 1, 3, 3, 1, 3, 3, 0, 4, 3, 0, 3, 0, 3, 4, 0, 3,
            3, 0, 3, 1, 3, 3, 0, 4, 0, 3, 3, 1, 3, 4, 1, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0,
            4, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 4, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3,
            3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [
            1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2,
            0, 2, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0,
            2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 4, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 4, 0, 2, 3,
            0, 3, 3, 1, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 2, 3, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 0, 4,
            3, 0, 3, 3, 1, 3, 4, 0, 4, 0, 3, 3, 0, 4, 2, 0, 2, 0, 3, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 4, 2, 0, 3, 0, 4, 3,
            0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 3, 0, 3, 1, 3, 3, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 0, 3,
            3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 4, 3, 0, 3, 0, 4, 3, 1, 3, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2,
            3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0,
        ],
        'guifvs': [0, 0, 0],
        'guifvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2,
            2, 0, 1, 0, 1, 1, 1, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 4, 0, 3, 2, 1, 2, 2, 0, 2, 1, 2, 2, 0, 3, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0,
            4, 3, 0, 3, 4, 0, 3, 0, 3, 3, 1, 3, 3, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 3, 0, 2,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            3, 2, 0, 3, 3, 0, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifns': [0, 0, 0],
        'guifnn': [
            1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 3,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 1, 2, 3, 0, 2, 3, 0,
            3, 1, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 5, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 5, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3,
            3, 0, 3, 3, 0, 3, 1, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 1, 3, 1, 0, 4, 0, 3, 4, 1, 3, 4,
            0, 5, 4, 0, 4, 1, 4, 4, 0, 4, 1, 4, 3, 0, 3, 3, 0, 3, 1, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 4, 0,
            4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3,
            0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 4, 1, 3, 4, 0, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [
            1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 2, 0, 2,
            0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0,
            3, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 3, 0, 2, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 4, 0, 3,
            0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 4, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [0, 0, 0],
        'guinvn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0,
            3, 4, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 2, 0, 2, 3, 1, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0,
            3, 4, 0, 3, 3, 0, 3, 1, 3, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 0, 4, 0,
            3, 3, 0, 3, 3, 0, 4, 1, 3, 4, 0, 4, 4, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 3, 0, 4, 3, 0, 3, 0,
            3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3,
            3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 3, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 3, 0, 3, 1,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 4, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4,
            4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 3, 1, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 5, 0, 5, 4, 1, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 6, 5, 0, 5, 5, 0, 5, 0,
            5, 5, 0, 5, 5, 0, 6, 0, 5, 5, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        ],
        'guicts': [0, 0, 0],
        'guictn': [
            1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 4, 0, 3, 3, 0, 2, 0, 3, 2, 0, 2, 3, 0, 2, 1, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            1, 3, 3, 0, 3, 1, 3, 3, 0, 3, 2, 0, 3, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 0, 3, 0, 3, 0, 3, 3, 0, 3, 3, -1, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, -1, 3, 0, 3, 2, 0, 2, 2, 0, 1, 0, 3, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 2, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, -1, 3, 3, 0, 3, 3, 0, 3, -1, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, -1,
        ],
        'guifts': [0, 0, 0],
        'guiftn': [
            1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 2, 2, 0, 3, 3, 0, 3, 0, 3, 3, -1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, -1, 3, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 3, 3, -1, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 4, -1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 3,
            0, 3, 0, 3, 2, 0, 3, 3, -1, 3, 0, 3, -1, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, -1,
            0, 0, 0, 0, 0, 0, 0, -1,
        ],
        'guints': [0, 0, 0],
        'guintn': [
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, -1,
            2, 2, 0, 1, 2, 0, 2, -1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, -1, 4, 4, 0, 4, 0, 3, 4, -1, 4, 4, 0, 4, -1, 4, 0, 0, 3, 4, 0, 4,
            0, 4, 4, -1, 4, 4, 0, 4, 0, 4, 3, 0, 4, 4, -1, 4, 0, 3, 2, 0, 2, 3, 0, 3, 0, 3, 4, 0, 4, 3, 0, 4, -1, 4, 4,
            -1, 4, 4, 0, 3, 0, 4, 4, -1, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, -1, 3, 3, -1, 3, 3, 0, 4, 0, -1, 4, -1, 4, 4, -1,
            4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4,
            0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 4, 0, 4,
        ],
    },

    'K2': {
        'stand': 1,
        'squat': 0.83,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 2, 0,
            2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2,
            0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 2, 0,
            3, 0, 2, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 2, 3,
            0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [
            2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0,
            2, 3, 0, 2, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 5, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 4, 1, 3, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 0, 3, 0, 4, 4, 0, 5, 4, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 0, 5, 3, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 4, 0, 4, 0,
            4, 3, 1, 4, 4, 1, 4, 0, 4, 4, 1, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 3, 0, 4,
            3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 1,
            4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 5, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 4, 0, 4, 4, 1, 4, 4, 0, 5, 0, 4, 4, 1, 4, 4, 1, 4, 0,
            1, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'FAMAS': {
        'stand': 1,
        'squat': 0.80,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [
            1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 2, 3, 1, 3, 0, 4, 3, 1, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            4, 0, 3, 0, 6, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 5, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 4, 3, 0, 5, 3, 0, 3, 0, 3, 3, 0, 4, 0, 3, 3, 1, 3, 3, 1, 4, 0, 3, 6, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 5, 0, 3, 0, 5, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 4, 0, 4, 3, 0, 5, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [
            1, 0, 1, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 3, 0, 3, 0, 2, 2, 0, 2, 1, 2, 3, 0, 2, 5, 0, 3, 0, 4, 3, 0, 3, 3, 0, 4, 4, 0, 4, 0, 5, 4, 0,
            4, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 5, 0, 4, 4, 1, 4, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3, 0, 4, 4,
            0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 4, 0, 4, 3, 0, 4, 4, 0, 4, 4, 0, 4, 1, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0,
            3, 3, 0, 4, 0, 3, 5, 0, 3, 1, 3, 3, 0, 3, 3, 0, 5, 0, 3, 5, 0, 4, 4, 0, 4, 0, 5, 3, 0, 3, 3, 1, 3, 3, 0, 4,
            0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 0, 4, 3, 0, 3, 4, 1, 3, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 4, 1, 3, 3, 0, 3, 0, 4, 3, 0, 4, 3, 0, 5, 0, 4, 4, 0,
            4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0,
            2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 0, 3, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3,
            0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 5, 4, 0, 6, 0, 4, 4, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0,
            5, 0, 4, 6, 0, 4, 4, 1, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 6, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 5, 0, 4, 6, 0, 4, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0,
            4, 4, 0, 4, 0, 5, 5, 0, 6, 0, 4, 5, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 5, 4, 0, 6, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 5, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'M249': {
        'stand': 1.50,
        'squat': 0.85,
        'lie': 0.45,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0,
            1, 1, 1, 1, 0, 2, 1, 0, 1, 2, 0, 1, 0, 2, 1, 0, 2, 1, 0, 1, 1, 1, 1, 0, 1, 2, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1,
            0, 1, 2, 0, 2, 1, 1, 2, 0, 4, 2, 0, 2, 0, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 0, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1,
            0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 2, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 1, 1, 0,
            1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 1,
            0, 1, 1, 0, 1, 2, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0
        ],
        'guinnn': [
            2, 0, 1, 0, 2, 1, 0, 1, 0, 1, 2, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 1, 1, 1, 1, 1, 2, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1,
            2, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 2, 0, 2, 0, 1, 2, 0, 1, 1, 1, 1, 0,
            2, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            1, 0, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1,
            0, 2, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1,
            0, 0, 1, 0, 1, 1, 0, 2, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0,
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'TOMMY': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [
            1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0,
            3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 5, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 1, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0,
            3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4,
            0, 4, 3, 0, 3, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3,
            0, 2, 4, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 4, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 1, 4, 0, 5, 4,
            1, 4, 5, 0, 4, 1, 4, 4, 0, 4, 4, 0, 4, 1, 4, 5, 0, 5, 4, 0, 4, 4, 0, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0,
            4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 4, 0, 0, 4, -1, 4, 0, 3, 4, 0, 3, 0, 4, 3,
            0, 4, 4, 0, 4, 0, 5, 5, 0, 4, 5, 0, 5, 0, 5, 5, 0, 5, 5, -1, 3, 4, 0, 3, 0, 4, 4, 0, 3, 0, 4, 4, -1, 4, 4,
            -1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 3, 0, 5, 0, 4, 6, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 5, 5, 0, 5, 0, 4, 4, 0, 5, 4, 0, 4, 1, 5, 5, 1, 5, 6, 1, 4, 1, 4, 4, 0, 4, 4,
            0, 4, 5, 0, 4, 1, 4, 4, 0, 4, 1, 4, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 6, 0, 4, 7, 0, 4, 0, 4,
            5, 0, 4, 4, 0, 4, 4, 0, 4, 1, 4, 4, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 6,
            0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            5, 0, 4, 6, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'UMP45': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [
            1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 1, 0, 1, 1, 0, 1,
            2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2,
            2, 0, 2, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 3, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0,
            2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 3, 0, 2,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0,
            3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 2, 0,
            3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 1, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [0, 0, 0],
        'guinvn': [
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 1, 1, 0, 1,
            0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 1, 2, 1, 0, 3, 0, 1, 2, 0,
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 1, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 3, 0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3,
            0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 4, 0,
            2, 2, 0, 2, -1, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 4, 0, 2, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 3, 0, 2, 0, 3, 3, 1, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 1, 2, 2, 0, 2, 2, 0, 3, 0, 3, 2, 0, 3, 4,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 3, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            4, 0, 3, 4, 0, 3, 0, 3, 3, 0, 3, 1, 2, 2, 0, 2, 2, 0, 2, 3, 1, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3,
            0, 3, 0, 3, 4, 0, 3, 3, 0, 2, 0, 2, 2, 1, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4,
            3, 0, 3, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 2, 0, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 2,
            1, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [
            1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0,
            3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
        ],
    },

    'PP19': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 1, 2, 2, 0, 2, 2, 1, 2, 0, 3, 2, 0, 3, 2, 0, 3, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 1, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 1, 0, 2, 0, 1, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 2, 0, 2, 2, 0, 2, 2, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2,
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 1, 2, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'VECTOR': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 6, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4,
            0, 4, 0, 5, 4, 0, 6, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 5, 0, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 5, 0, 0, 0, 0, 0, 0,
        ],
        'guinhn': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 3, 0, 2, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 6, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4,
            0, 5, 0, 4, 6, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 5, 0, 4, 5, 0, 5, 0, 5, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            5, 0, 4, 0, 5, 5, 0, 5, 4, 0, 4, 4, 0, 4, 1, 4, 4, 1, 4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 1, 4, 4,
            0, 4, 0, 5, 0, 1, 0, 0, 0
        ],
        'guinvs': [
            1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 0,
            3, 3, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 5, 0, 4, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4,
            4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 0,
            4, 4, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0
        ],
        'guinvn': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 5, 0, 4, 6, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 5, 0, 4, 1, 6, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 4, 0, 5, 5, 0, 5, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0,
            4, 0, 5, 4, 0, 5, 5, 0, 5, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 4, 4, 0, 4, 0, 4, 4,
            0, 4, 4, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 2, 0, 2, 1, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 4, 3, 0, 3, 1, 3, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 5, 4, 0,
            5, 0, 4, 6, 0, 5, 5, 0, 4, 0, 5, 4, 0, 6, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 4, 0, 4, 0, 5, 4, 0, 5, 0, 5, 5,
            0, 5, 5, 0, 5, 0, 5, 5, 0, 4, 4, 0, 5, 4, 0, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0,
            5, 0, 5, 5, 0, 5, 6, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 1, 5, 5, 0, 5, 6, 0, 6, 0, 5, 5, 0, 5, 5, 0, 5,
            0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinnn': [
            2, 0, 1, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 1, 2, 0, 2, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 3, 1, 3, 0, 3, 3, 0, 4, 4, 1, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 5, 4, 0,
            5, 4, 1, 6, 0, 6, 4, 0, 5, 5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 4, 0, 4, 0, 5, 4, 0, 5, 5, 1, 5,
            0, 6, 5, 1, 5, 5, 0, 6, 0, 4, 4, 0, 5, 4, 0, 6, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 6, 5, 1, 5, 1, 5, 5, 0,
            5, 5, 0, 5, 1, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5,
            0, 6, 5, 0, 5, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MICROUZI': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2, 1, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 4, 0, 3, 5, 0, 4, 1, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 5, 0, 5, 0, 5,
            5, 0, 4, 0, 5, 5, 0, 5, 5, 0, 6, 5, 0, 5, 0, 5, 6, 0, 5, 4, 1, 5, 1, 4, 5, 0, 5, 6, 0, 6, 0, 6, 6, 0, 6, 5,
            0, 5, 0, 5, 6, 0, 5, 6, 0, 6, 0, 5, 5, 1, 5, 0, 5, 5, 0, 5, 5, 0, 5, 0, 5, 5, 0, 5, 5, 0, 5, 1, 5, 5, 0, 6,
            5, 0, 5, 6, 0, 6, 0, 6, 6, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 6, 0, 5, 0, 5, 5, 0, 5, 0, 5, 6, 0, 1, 0, 0, 0, 0,
            0, 0, 0, 0, 1, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MP5K': {
        'stand': 1,
        'squat': 0.73,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 4, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2,
            0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0,
            3, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0, 4, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 0, 3, 3,
            0, 3, 3, 0, 3, 0, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 5, 4, 0, 3, 0, 3, 3, 0, 4, 3, 0, 3, 0, 4, 3, 1, 3, 4, 0,
            3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 3, 1, 3, 3, 1, 3, 0, 3, 4, 0, 3, 3, 0, 4, 0, 3, 3, 0, 4, 3, 1, 4, 0, 4, 3,
            1, 3, 3, 0, 4, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 5, 0, 3, 0, 4, 4, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 4, 0,
            4, 0, 3, 3, 1, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 4, 0, 3, 0, 4, 3,
            0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinhn': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 4, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 4,
            3, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 3, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 5, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 1, 3, 0, 3,
            3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 3, 3, 0, 3, 1, 3, 3, 0, 4, 3, 1, 3, 0, 4, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3,
            3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3,
            0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinvs': [
            2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2,
            0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3, 3,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0
        ],
        'guinvn': [
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 0, 1, 2, 0, 2,
            2, 0, 2, -1, 3, 2, -1, 3, 3, 0, 3, -1, 3, 2, 0, 2, 2, 0, 2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 3, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, -1, 3, 0, 3, 2, 0, 3, 0, 3, 1, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 3, 3, -1, 3, 0, 3, 3, 0, 3, 0, 3, 1,
            0, 2, 2, -1, 2, 0, 1, 3, 0, 1, 3, 0, 3, 0, 3, 2, 0, 3, 3, -1, 3, 3, 0, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2,
            0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 3, 0, 3,
            3, -1, 3, 3, -1, 2, 0, 1, 2, 0, 1, 2, 0, 2, 0, 2, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinns': [
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 5, 0, 5, 0,
            4, 4, 0, 4, 3, 0, 4, 0, 3, 4, 0, 5, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3, 1, 3, 4, 0, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 1, 3, 0, 3, 4, 0, 4, 3, 0, 4, 0,
            4, 5, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            4, 3, 0, 4, 4, 0, 4, -1, 4, 4, 0, 4, 4, 0, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, -1, 4, 4, 0,
            4, 3, 0, 4, -1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guinnn': [
            2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 4, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 1, 0, 5, 0, 5, 0, 4, 4, 0,
            4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 3, 0, 3, 4, 0, 3,
            0, 3, 4, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0,
            4, 4, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 4, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 4, 0, 4, 3, 0, 4, 4, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3, -1, 3, 3, 0, 3, 4, 0, 3, 0, 4, 4, 0, 4, 4, 0, 4,
            0, 4, 4, -1, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 4, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [
            2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, -1, 2, 3, 0, 3, 0, 2, 3, -1, 3, 2, 0, 2, 0, 3, 2, 0, 3,
            3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0,
            4, 3, 1, 4, 5, 0, 3, 0, 4, 3, 0, 3, 3, 1, 3, 1, 3, 3, 1, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 4, 4, 0, 3, 3, 0, 3,
            0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, -1, 3,
            -1, 3, 4, -1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 2, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 4, 3, 0, 4, 0, 4, 3,
            0, 4, 3, 0, 3, 0, 3, 3, 0, 2, 3, 0, 3, 0, 3, 2, 0, 0, 4, 0, 3, -1, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0,
            3, 0, 2, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 4, 4, 0, 4, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 4, 3,
            0, 3, 4, 0, 4, 0, 4, 3, -1, 3, 3, 0, 3, -1, 3, 3, 0, 3, 3, -1, 3, 0, -1, -1, 0, -1, 0, -1, 0, -1, 0, 0, 0,
            -1,
        ],
        'guintn': [
            2, 2, 0, 2, 2, -1, 2, 2, 0, -1, 0, 2, 2, 0, 3, 2, -1, 3, 0, 3, 2, 0, 3, -1, 3, 2, 0, 3, 3, 0, 3, 3, 0, 3, 0,
            3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 3, 0, 4, 0, 3, 4, 0, 3,
            1, 4, 4, 0, 3, 3, 0, 4, 0, 3, 3, 0, 3, 3, 0, 4, 3, 0, 3, 1, 4, 3, 0, 4, 0, 4, 4, 0, 4, 3, 0, 3, 0, 3, 3, 0,
            3, 3, 0, 3, 3, 0, 3, 0, 3, 4, 0, 3, 4, 0, 4, 0, 3, 4, -1, 3, 0, 3, 2, 0, 3, 3, 0, 3, 2, 0, 3, 0, 3, 2, 0, 4,
            2, 0, 4, 0, 4, 4, 0, 4, -1, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, -1, 4, 3, 0, 4, 4, 0, 3, 4, 0, 3,
            0, 3, 2, 0, 2, 3, 0, 3, 0, 3, 3, -1, 3, 3, 0, 3, 0, 3, 4, 0, 4, 4, 0, 3, 0, 3, 3, 0, 3, -1, 3, 3, 0, 3, 3,
            0, 3, 2, 0, 3, 0, 3, 3, 0, 4, 4, 0, 3, 0, 4, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 3, 3, -1, 3, 4, 0, 3, 0,
            -1, 0, 0, 3, 4, 0, 3, 2, 0, 3, -1, 3, 3, 0, 3, 0, 2, 3, 0, 2, 3, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0,
            -1, 0, -1, 0, -1, 0, -1, 0, -1, -1, -1, -1, -1,
        ],
    },

    'MP9': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            1, 1, 0, 1, 1, 0, 2, 1, 1, 1, 2, 0, 2, 1, 1, 2, 0, 2, 2, 0, 2, 1, 0, 2, 1, 0, 1, 2, 0, 2, 1, 2, 2, 1, 1, 2,
            1, 1, 2, 0, 2, 2, 1, 2, 1, 2, 2, 1, 2, 2, 1, 2, 1, 0, 1, 1, 1, 1, 0, 2, 1, 1, 2, 2, 0, 1, 2, 0, 2, 1, 0, 2,
            1, 1, 1, 1, 1, 2, 1, 2, 2, 1, 2, 3, 1, 1, 1, 2, 2, 0, 2, 1, 1, 1, 2, 0, 2, 1, 1, 1, 1, 1, 2, 0, 2, 1, 0, 2,
            2, 0, 2, 1, 1, 1, 2, 0, 2, 2, 0, 2, 0, 1, 2, 0, 1, 1, 0, 2, 1, 0, 2, 0, 1, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 1,
            1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 2, 1, 0,
            1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 2, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'JS9': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            2, 0, 2, 2, 0, 2, 0, 2, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 2, 3, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2,
            0, 3, 2, 0, 2, 0, 2, 2, 1, 2, 2, 0, 2, 0, 2, 2, 0, 3, 2, 0, 3, 2, 0, 2, 1, 2, 2, 0, 2, 2, 1, 2, 0, 2, 3, 0,
            2, 2, 0, 3, 1, 2, 4, 0, 3, 3, 0, 2, 0, 4, 2, 1, 3, 3, 1, 3, 1, 3, 4, 0, 3, 3, 0, 3, 0, 4, 3, 0, 3, 3, 0, 3,
            0, 0, 2, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 4, 0, 3, 3, 1, 3, 0, 3, 2, 1, 2, 3, 1, 2, 0, 4, 3, 0, 3, 4, 0, 3, 0,
            3, 3, 1, 3, 3, 0, 3, 1, 3, 2, 1, 2, 3, 1, 2, 0, 3, 3, 1, 3, 3, 1, 3, 1, 3, 3, 1, 3, 3, 0, 3, 0, 3, 3, 1, 3,
            3, 0, 3, 0, 3, 4, 0, 3, 3, 0, 3, 1, 3, 3, 0, 3, 4, 1, 3, 0, 3, 4, 0, 3, 3, 0, 4, 0, 4, 3, 0, 3, 3, 0, 3, 1,
            3, 3, 0, 3, 4, 0, 3, 1, 3, 3, 1, 3, 3, 0, 3, 0, 4, 3, 0, 3, 4, 0, 3, 0, 4, 3, 0, 3, 4, 0, 3, 0, 3, 3, 0, 4,
            3, 0, 4, 3, 0, 3, 1, 3, 4, 0, 4, 3, 1, 3, 0, 4, 4, 0, 3, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0,
            0, 1, 0, 1, 0, 1, 0, 1, 0, 1,
    ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'P90': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [
            3, 2, 0, 2, 0, 3, 2, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 2, 0, 1, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2,
            0, 2, 2, 0, 2, 2, 0, 2, 0, 3, 2, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 0, 2, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 3, 0, 2, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 3, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 3, 0, 2,
            3, 0, 3, 0, 3, 3, 0, 3, 0, 3, 3, 0, 3, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
            2, 1, 0, 2, 2, 0, 2, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 2,
            2, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0, 1, 1, 0,
            1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 0, 2, 2, 0, 2, 2, 0, 1, 0, 1, 1, 0, 1, 2, 0, 1,
            2, 0, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 1, 0, 1, 0, 1, 1, 0, 2, 1, 0, 2, 2, 0, 2, 0,
            2, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0,
        ],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'M16A4': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MK47': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],

    },

    'AWM': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'DRAGUNOV': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'KAR98K': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'LYNXAMR': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'M24': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MINI14': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MK12': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'QBU': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MK14': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MOSINNAGANT': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'SKS': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'VSS': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'SLR': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'WIN94': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'CROSSBOW': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'MORTAR': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'PANZERFAUST': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'DBS': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'O12': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'S12K': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'S686': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'S1897': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    'nothing': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },

    '-': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],

    },

    '--': {
        'stand': 1,
        'squat': 0.75,
        'lie': 0.55,
        'guichs': [0, 0, 0],
        'guichn': [0, 0, 0],
        'guicvs': [0, 0, 0],
        'guicvn': [0, 0, 0],
        'guicns': [0, 0, 0],
        'guicnn': [0, 0, 0],
        'guifhs': [0, 0, 0],
        'guifhn': [0, 0, 0],
        'guifvs': [0, 0, 0],
        'guifvn': [0, 0, 0],
        'guifns': [0, 0, 0],
        'guifnn': [0, 0, 0],
        'guinhs': [0, 0, 0],
        'guinhn': [0, 0, 0],
        'guinvs': [0, 0, 0],
        'guinvn': [0, 0, 0],
        'guinns': [0, 0, 0],
        'guinnn': [0, 0, 0],
        'guicts': [0, 0, 0],
        'guictn': [0, 0, 0],
        'guifts': [0, 0, 0],
        'guiftn': [0, 0, 0],
        'guints': [0, 0, 0],
        'guintn': [0, 0, 0],
    },
}
