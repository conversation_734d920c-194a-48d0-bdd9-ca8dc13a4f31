from threading import Thread
from PyQt5.QtCore import QThread, pyqtSignal
import win32api
import win32con
import time

class AppMainMouseListener(QThread):
    mouseClicked = pyqtSignal(str, tuple)
    
    def __init__(self, PCdata):
        super().__init__()
        self.PC = PCdata
        self.count = 1
        self.running = False
        self.debug = False

    def print_debug(self, message):
        """Вывод отладочной информации"""
        if self.debug:
            print(message)

    def run(self):
        """Запуск мониторинга мыши"""
        self.rerun()

    def rerun(self):
        """Перезапуск мониторинга"""
        self.running = True
        self.mouseClicked.emit("l", ("Mouse monitoring started...",))
        
        prev_left = False
        prev_right = False
        prev_middle = False
        prev_x1 = False
        prev_x2 = False
        
        while self.running:
            try:
                left_pressed = win32api.GetKeyState(win32con.VK_LBUTTON) < 0
                right_pressed = win32api.GetKeyState(win32con.VK_RBUTTON) < 0
                middle_pressed = win32api.GetKeyState(win32con.VK_MBUTTON) < 0
                x1_pressed = win32api.GetKeyState(win32con.VK_XBUTTON1) < 0
                x2_pressed = win32api.GetKeyState(win32con.VK_XBUTTON2) < 0

                if left_pressed != prev_left:
                    self.on_left_click(left_pressed)
                    prev_left = left_pressed

                if right_pressed != prev_right:
                    self.on_right_click(right_pressed)
                    prev_right = right_pressed

                if middle_pressed != prev_middle:
                    self.on_middle_click(middle_pressed)
                    prev_middle = middle_pressed

                if x1_pressed != prev_x1:
                    self.on_x_click(True, x1_pressed)
                    prev_x1 = x1_pressed

                if x2_pressed != prev_x2:
                    self.on_x_click(False, x2_pressed)
                    prev_x2 = x2_pressed

                time.sleep(0.001)

            except Exception as e:
                self.print_debug(f"Error in mouse monitoring: {e}")
                time.sleep(0.1)

    def on_left_click(self, pressed):
        """Обработка нажатия левой кнопки мыши"""
        try:
            self.PC.mouse_one = pressed
            if pressed and self.PC.StartFire:
                macro.macro_enabled.set()
                self.mouseClicked.emit('l', (f"Shot {self.count}",))
                Thread(target=self.PC.FIRE_Start, args=(self.mouseClicked.emit,)).start()
                self.count += 1
        except Exception as e:
            self.print_debug(f"Error in left click handler: {e}")

    def on_right_click(self, pressed):
        """Обработка нажатия правой кнопки мыши"""
        try:
            if not self.PC.TabKey:
                if self.PC.RightClick:
                    self.PC.StartFire = pressed
                else:
                    if pressed:
                        Thread(target=self.PC.IF_Open_Lens).start()
                self.mouseClicked.emit('s', (self.PC.StartFire,))
        except Exception as e:
            self.print_debug(f"Error in right click handler: {e}")

    def on_middle_click(self, pressed):
        """Обработка нажатия средней кнопки мыши"""
        try:
            # Toggle macro on middle mouse button press (not release)
            if pressed:
                import macro
                macro_state = macro.toggle_macro()
                if macro_state:
                    if hasattr(self.PC, 'overlay') and self.PC.overlay and self.PC.overlay.isVisible():
                        self.PC.overlay.update_info(
                            macro_status=macro_state["enabled"]
                        )
                    self.mouseClicked.emit('l', (macro_state["status"],))
            
            # Still emit the original event for compatibility
            self.mouseClicked.emit('mag', (pressed,))
        except Exception as e:
            self.print_debug(f"Error in middle click handler: {e}")

    def on_x_click(self, is_x1, pressed):
        """Обработка нажатия дополнительных кнопок мыши"""
        try:
            if pressed:
                self.PC.StartFire = False
            self.mouseClicked.emit('s', (self.PC.StartFire,))
        except Exception as e:
            self.print_debug(f"Error in X button handler: {e}")

    def stop_listener(self):
        """Остановка мониторинга"""
        self.running = False
        self.mouseClicked.emit("l", ("Mouse monitoring stopped...",))
