Stack trace:
Frame         Function      Args
0007FFFFBFB0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAEB0) msys-2.0.dll+0x2118E
0007FFFFBFB0  0002100469BA (000000000000, 000000000000, 000000000000, 6FFFFFFE048A) msys-2.0.dll+0x69BA
0007FFFFBFB0  0002100469F2 (00021028DF99, 0007FFFFBE68, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBFB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBFB0  00021006A545 (0007FFFFBFC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
000000000006  00021006B9A5 (0007FFFFBFC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 zsh.exe
7FFFC57D0000 ntdll.dll
7FFFC4410000 KERNEL32.DLL
7FFFC2C90000 KERNELBASE.dll
000210040000 msys-2.0.dll
000539B50000 msys-zsh-5.9.dll
0005FCB10000 msys-ncursesw6.dll
7FFFC55C0000 advapi32.dll
7FFFC4960000 msvcrt.dll
7FFFC4A30000 sechost.dll
7FFFC3190000 bcrypt.dll
7FFFC4B70000 RPCRT4.dll
7FFFC1910000 CRYPTBASE.DLL
7FFFC3280000 bcryptPrimitives.dll
