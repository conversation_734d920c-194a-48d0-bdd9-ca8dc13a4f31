import json
from PyQt5.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPainter, QColor
import win32gui
import win32con

class InfoOverlay(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_window()
        self.setup_timer()
        self.setWindowOpacity(0.5)
        self.listen_mode_label = QLabel("Slot: both")
        self.layout.addWidget(self.listen_mode_label)
        
    def setup_window(self):
        self.setWindowTitle("PUBG Info")
        self.setWindowFlags(
            Qt.FramelessWindowHint | 
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.resize(200, 150)

        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            if screen:
                geometry = screen.geometry()
                # Pastikan overlay muncul di area yang terlihat (posisi kanan atas)
                x_pos = min(geometry.width() - 220, 1700)
                y_pos = 10
                self.move(x_pos, y_pos)
                print(f"Positioned overlay at {x_pos}, {y_pos}, screen size: {geometry.width()}x{geometry.height()}")
        else:
            # Posisi default yang aman untuk 1920x1080
            self.move(1700, 10)
            print("Using default position 1700, 10")

        # Make the window transparent for input events (click-through)
        self.setWindowFlags(self.windowFlags() | Qt.WindowTransparentForInput)
        hwnd = self.winId().__int__()
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
        win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, 
                             style | win32con.WS_EX_TRANSPARENT | win32con.WS_EX_LAYERED)

    def setup_ui(self):
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)

        self.weapon1_label = QLabel("Slot 1: No weapon")
        self.weapon2_label = QLabel("Slot 2: No weapon")
        self.macro_label = QLabel("Macro: Off")
        self.tab_status_label = QLabel("Tab: Enabled")
        self.recoil1_label = QLabel("Recoil 1: ---")
        self.recoil2_label = QLabel("Recoil 2: ---")
        self.stance_label = QLabel("Stance: Standing")
        self.current_slot_label = QLabel("Slot: -")

        style = """
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """
        for label in [self.weapon1_label, self.weapon2_label, self.macro_label, 
                     self.tab_status_label, self.recoil1_label, self.recoil2_label, 
                     self.stance_label, self.current_slot_label]:
            label.setStyleSheet(style)
            self.layout.addWidget(label)

    def setup_timer(self):
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.force_update)
        self.update_timer.start(100)

    def force_update(self):
        if hasattr(self, '_process'):
            self._process.update_overlay_info()

    def set_process(self, process):
        self._process = process
        # Save current scope values before reloading config
        current_scope_values = {}
        if hasattr(process, 'ScopeData'):
            current_scope_values = {k: v for k, v in process.ScopeData.items()}
            print("Saving current scope values before overlay initialization:")
            for scope, value in current_scope_values.items():
                if scope in ['8x', '8bei', '4x', '4bei']:
                    print(f"  - {scope}: {value}")
        
        # Reload scope configuration but preserve custom values
        if hasattr(process, 'reload_scope_config'):
            process.reload_scope_config()
            
            # Restore any custom scope values that might have been reset
            if current_scope_values:
                for scope, value in current_scope_values.items():
                    if scope in ['8x', '8bei'] and value != 8.0:
                        print(f"Restoring custom scope value after overlay init: {scope} = {value}")
                        process.ScopeData[scope] = value
                        
                        # Also update macro if available
                        try:
                            import macro
                            macro.scope_multipliers[scope] = value
                        except:
                            pass

    def update_info(self, weapon1=None, weapon2=None, macro_status=None, tab_status=None, recoil1=None, recoil2=None, stance="Standing", listen_mode="both", current_slot="-"):
        try:
            if weapon1 is not None:
                self.weapon1_label.setText(f"Slot 1: {weapon1}")

                if weapon2 is not None:
                    self.weapon2_label.setText(f"Slot 2: {weapon2}")

                if recoil1 is not None:
                    self.recoil1_label.setText(f"Recoil 1: {recoil1}")

                if recoil2 is not None:
                    self.recoil2_label.setText(f"Recoil 2: {recoil2}")

                if macro_status is not None:
                    color = "lime" if macro_status else "red"
                    self.macro_label.setStyleSheet(f"color: {color}; font-size: 14px; font-weight: bold;")
                    self.macro_label.setText(f"Macro: {'Enabled' if macro_status else 'Disabled'}")

                if tab_status is not None:
                    color = "red" if tab_status else "lime"
                    self.tab_status_label.setStyleSheet(f"color: {color}; font-size: 14px; font-weight: bold;")
                    self.tab_status_label.setText(f"Tab: {'Disabled' if tab_status else 'Enabled'}")

                if stance is not None:
                    self.stance_label.setText(f"Stance: {stance}")

                mode_text = {
                    "both": "Slot: both",
                    "1": "Slot: 1",
                    "2": "Slot: 2"
                }
                self.listen_mode_label.setText(mode_text.get(listen_mode, "Slot: both"))
                self.listen_mode_label.setStyleSheet("color: #00ff00; font-weight: bold;")

                self.current_slot_label.setText(f"Active Slot: {current_slot}")
                self.current_slot_label.setStyleSheet("color: #00ff00; font-size: 14px; font-weight: bold;")

                self.update()

            
        except Exception as e:
            print(f"Error updating overlay info: {e}")
            import traceback
            traceback.print_exc()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        painter.fillRect(self.rect(), QColor(0, 0, 0, 180))

        painter.setPen(QColor(100, 100, 100, 200))
        painter.drawRect(self.rect().adjusted(0, 0, -1, -1))

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragPosition = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
        elif event.button() == Qt.RightButton:
            # Reset posisi dengan klik kanan
            self.reset_position()
            event.accept()

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton:
            self.move(event.globalPos() - self.dragPosition)

            self.save_position()
            event.accept()

    def ensure_visible_on_screen(self):
        """Memastikan overlay selalu terlihat pada layar"""
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    geometry = screen.geometry()
                    screen_width = geometry.width()
                    screen_height = geometry.height()
                    
                    # Dapatkan posisi dan ukuran overlay saat ini
                    overlay_x = self.x()
                    overlay_y = self.y()
                    overlay_width = self.width()
                    overlay_height = self.height()
                    
                    # Pastikan overlay tidak keluar dari layar
                    if overlay_x + overlay_width > screen_width:
                        overlay_x = screen_width - overlay_width - 10
                    if overlay_y + overlay_height > screen_height:
                        overlay_y = screen_height - overlay_height - 10
                    if overlay_x < 0:
                        overlay_x = 10
                    if overlay_y < 0:
                        overlay_y = 10
                        
                    # Terapkan posisi baru jika perlu
                    if overlay_x != self.x() or overlay_y != self.y():
                        self.move(overlay_x, overlay_y)
                        print(f"Adjusted overlay position to: {overlay_x}, {overlay_y}")
        except Exception as e:
            print(f"Error ensuring overlay visibility: {e}")

    def show(self):
        print("Overlay show() called")
        super().show()
        self.raise_()
        self.activateWindow()
        
        # Pastikan overlay terlihat pada layar
        self.ensure_visible_on_screen()
        
        # Ensure window stays on top and transparent for input after showing
        hwnd = self.winId().__int__()
        win32gui.SetWindowPos(
            hwnd,
            win32con.HWND_TOPMOST,
            0, 0, 0, 0,
            win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
        )

    def save_position(self):
        """Сохраняет текущую позицию оверлея в файл конфигурации"""
        try:
            pos = {
                'x': self.x(),
                'y': self.y()
            }
            with open('./Config/overlay_position.json', 'w') as f:
                json.dump(pos, f, indent=4)
            print(f"Overlay position saved: {pos}")
        except Exception as e:
            print(f"Error saving overlay position: {e}")

    def reset_position(self):
        """Reset posisi overlay ke posisi default yang aman"""
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    geometry = screen.geometry()
                    screen_width = geometry.width()
                    
                    # Posisi default di kanan atas
                    x_pos = min(screen_width - 220, 1700)
                    y_pos = 10
                    
                    self.move(x_pos, y_pos)
                    print(f"Reset overlay position to: {x_pos}, {y_pos}")
                    
                    # Simpan posisi baru
                    self.save_position()
                    return True
        except Exception as e:
            print(f"Error resetting overlay position: {e}")
        
        # Fallback ke posisi default
        self.move(1700, 10)
        self.save_position()
        return True