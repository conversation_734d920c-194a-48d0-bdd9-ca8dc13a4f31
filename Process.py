import simplejson as json
import os
import sys
import threading
import time
import concurrent.futures
from recognition import capture_all_positions_thread, recogniseif_firearm
from fire_data import KEY_DATA
import asyncio
import numpy as np
from pyopdll import OP
import macro
from macro import calculate_total_multiplier, calculate_final_adjusted_down
from PyQt5.QtWidgets import QApplication
import win32api 
import win32con 

class ProcessClass:
    _instance_lock = threading.Lock()
    _Result1 = {}
    _Result2 = {}
    _gd = None

    @classmethod
    def get_recognition_results(cls):
        return cls._Result1, cls._Result2

    def __new__(cls, *args, **kwargs):
        if not hasattr(ProcessClass, "_instance"):
            with ProcessClass._instance_lock:
                if not hasattr(ProcessClass, "_instance"):
                    ProcessClass._instance = object.__new__(cls)
        return ProcessClass._instance

    def __init__(self):
        self.TabKey = False
        self.window_version = self.get_window_version()
        self.Monitor = self.get_config_data("r") or "2560x1440"
        self.mouse_one = False
        self.Current_firearms = None
        self.Current_posture = "None"
        self.sensitivity = 1
        self.StartFire = False
        self.RightClick = False
        self.clicking = False
        self.shift_pressed = False
        self.crouch_pressed = False
        self.crouch_toggle_mode = self.get_config_data('crouch_toggle') or False
        self.is_crouched = False
        
        # Initialize default scope data
        self.ScopeData = {
            'none': 1.0,
            'hongdian': 1.0,
            'quanxi': 1.0,    
            '2bei': 2.0,
            '3bei': 3.0,
            '4bei': 4.0,
            '6bei': 6.0,
            '8bei': 8.0,
            '15bei': 15.0,
            'shift': 0.0,
            '2x': 2.0,
            '3x': 3.0,
            '4x': 4.0,
            '6x': 6.0,
            '8x': 8.0,
            '15x': 15.0
        }

        # Load scope data from config
        print("Loading initial scope configuration...")
        config_scope_data = self.get_config_data('s')
        if isinstance(config_scope_data, dict) and config_scope_data:
            print("Found scope configuration in config file:")
            for scope, value in config_scope_data.items():
                print(f"  - {scope}: {value}")
                self.ScopeData[scope] = float(value)
                # Also update the x format if it's a bei format
                if 'bei' in scope:
                    x_format = scope.replace('bei', 'x')
                    self.ScopeData[x_format] = float(value)
        else:
            print("No valid scope configuration found in config, using defaults")
            
        self.GunsName = None
        self.added = False
        self.op = OP()
        self.crouch_key = self.get_config_data('crouch_key') or "ctrl_l"
        import macro
        macro.set_crouch_key(self.crouch_key)
        
        # Load sensitivity settings
        ads_sensitivity = self.get_config_data('ads_sensitivity')
        if ads_sensitivity:
            macro.set_ads_multiplier(ads_sensitivity)
        
        vertical_multiplier = self.get_config_data('vertical_multiplier')
        if vertical_multiplier:
            macro.set_vertical_multiplier(vertical_multiplier)
        
        scope_multiplier = self.get_config_data('scope_multiplier')
        if scope_multiplier:
            macro.set_scope_multiplier(scope_multiplier)
        
        # Initialize macro module's scope multipliers
        for scope, value in self.ScopeData.items():
            macro.scope_multipliers[scope] = float(value)
        
        self.gun1_data = None
        self.gun2_data = None
        self.listen_mode = self.get_config_data('listen_mode') or "both"
        self.crosshair_enabled = self.get_config_data('crosshair') or False
        self.crosshair_type = self.get_config_data('crosshair_type') or "simple"
        self.crosshair_color = self.get_config_data('crosshair_color') or "white"
        self.overlay_enabled = self.get_config_data('overlay') or False
        self.overlay = None
        self.crosshair_size = self.get_config_data('crosshair_size') or 1.0
        self.overlay_opacity = self.get_config_data('overlay_opacity') or 50
        self.debug = False

    def move_mouse(self, x, y):
        self.op.MoveR(x, y)

    def get_config_data(self, mode='r'):
        try:
            with open('./Config/config.json', "r", encoding='utf-8') as Config:
                config_data = json.loads(Config.read())
                if mode == 'r':
                    return config_data.get("resolution", "1920x1080")
                elif mode == 's':
                    sensitivity = config_data.get('sensitivity', {})
                    print(f"Loaded sensitivity data from config: {sensitivity}")
                    return sensitivity
                elif mode == 'a':
                    return config_data
                elif mode == 'crouch_key':
                    return config_data.get("crouch_key", "CTRL")
                elif mode == 'listen_mode':
                    return config_data.get("listen_mode", "both")
                elif mode == 'crosshair':
                    return config_data.get("crosshair", False)
                elif mode == 'crosshair_type':
                    return config_data.get("crosshair_type", "simple")
                elif mode == 'crosshair_color':
                    return config_data.get("crosshair_color", "white")
                elif mode == 'crouch_toggle':
                    return config_data.get("crouch_toggle", False)
                elif mode == 'crosshair_size':
                    return config_data.get("crosshair_size", 1.0)
                elif mode == 'overlay_opacity':
                    return config_data.get("overlay_opacity", 50)
                elif mode == 'crosshair_resolution':
                    return config_data.get("crosshair_resolution", "1920x1080")
                elif mode == 'ads_sensitivity':
                    return config_data.get("ads_sensitivity", 36)
                elif mode == 'vertical_multiplier':
                    return config_data.get("vertical_multiplier", 1.45)
                elif mode == 'scope_multiplier':
                    return config_data.get("scope_multiplier", 35)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error reading config: {e}")
            default_config = {
                "resolution": "1920x1080",
                "sensitivity": {
                    'none': 1.0,
                    'hongdian': 1.0,
                    'quanxi': 1.0,    
                    '2bei': 2.0,
                    '3bei': 3.0,
                    '4bei': 4.0,
                    '6bei': 6.0,
                    '8bei': 8.0,
                    '15bei': 15.0,
                },
                "crouch_key": "CTRL",
                "listen_mode": "both",
                "crosshair": False,
                "crosshair_type": "simple",
                "crosshair_color": "white",
                "overlay": False,
                "crouch_toggle": False,
                "crosshair_size": 1.0,
                "overlay_opacity": 50,
                "crosshair_resolution": "1920x1080",
                "ads_sensitivity": 36,
                "vertical_multiplier": 1.45,
                "scope_multiplier": 35
            }
            with open('./Config/config.json', "w", encoding='utf-8') as Config:
                json.dump(default_config, Config, indent=4)
            return default_config.get(mode) if mode != 'a' else default_config

    def save_config_data(self, mode, data):
        save_data = self.get_config_data('a')
        if mode == True:
            save_data['resolution'] = data
        elif mode == 'crouch_key':
            save_data['crouch_key'] = data
        elif mode == 'listen_mode':
            save_data['listen_mode'] = data
        elif mode == 'crosshair':
            save_data['crosshair'] = data
        elif mode == 'crosshair_type':
            save_data['crosshair_type'] = data
        elif mode == 'crouch_toggle':
            save_data['crouch_toggle'] = data
        elif mode == 'crosshair_resolution':
            save_data['crosshair_resolution'] = data
        elif mode == 'ads_sensitivity':
            save_data['ads_sensitivity'] = data
        elif mode == 'vertical_multiplier':
            save_data['vertical_multiplier'] = data
        elif mode == 'scope_multiplier':
            save_data['scope_multiplier'] = data
        elif mode == 'overlay_opacity':
            save_data['overlay_opacity'] = data
        else:
            save_data['sensitivity'] = data
        with open('./Config/config.json', "w", encoding='utf-8') as Config:
            Config.write(json.dumps(save_data))

    def reduction_data(self):
        self.StartFire = False
        self.clicking = False
        self.sensitivity = 1
        self.Current_posture = "None"
        self.Current_firearms = None
        self.mouse_one = False
        self.TabKey = False
        self._Result1 = {}
        self._Result2 = {}

    def read_gun_data(self, fileName) -> dict:
        if not os.path.exists(f'./_internal/GunData/{fileName}.json'):
            return
        with open(f'./_internal/GunData/{fileName}.json', "r", encoding='utf-8') as GUNS:
            GUNS_data = json.loads(GUNS.read())
            return GUNS_data

    def get_current_scope(self):
        result = self.get_guns_info()
        if result is None:
            return "none"
        scope = result["Scope"].lower()
        if scope.endswith('x'):
            return scope.replace('x', 'bei')
        return scope

    def shift_multiplier(self):
        """Обработка множителя shift с защитой от ошибок"""
        try:
            shift_scope = self.ScopeData.get('shift', 0.0)
            if not isinstance(shift_scope, (int, float)):
                shift_scope = 0.0
                
            if self.shift_pressed and shift_scope > 0:
                if self.Current_firearms and self.StartFire:
                    if not self.added:
                        accessor_scope = self.get_current_scope()

                        if accessor_scope in self.ScopeData:
                            self.ScopeData[accessor_scope] += float(shift_scope)
                            self.added = True
                            
        except Exception as e:
            print(f"Error in shift_multiplier: {e}")
            self.ScopeData['shift'] = 0.0

    def on_shift_pressed(self):
        self.shift_pressed = True
        self.shift_multiplier()

    def on_shift_released(self):
        self.shift_pressed = False
        # Reload scope configuration from config file
        config_scope_data = self.get_config_data('s')
        if isinstance(config_scope_data, dict) and config_scope_data:
            print("Restoring scope configuration after shift release:")
            for scope, value in config_scope_data.items():
                print(f"  - Restoring {scope}: {value}")
                self.ScopeData[scope] = float(value)
                # Also update the x format if it's a bei format
                if 'bei' in scope:
                    x_format = scope.replace('bei', 'x')
                    self.ScopeData[x_format] = float(value)
        else:
            # Fallback to default values if config loading fails
            self.ScopeData = {
                'none': 1.0,
                'hongdian': 1.0,
                'quanxi': 1.0,
                '2bei': 2.0,
                '3bei': 3.0,
                '4bei': 4.0,
                '6bei': 6.0,
                '8bei': 8.0,
                '15bei': 15.0,
                'shift': 0.0,
                '2x': 2.0,
                '3x': 3.0,
                '4x': 4.0,
                '6x': 6.0,
                '8x': 8.0,
                '15x': 15.0
            }
            print("Using default scope values after shift release")
            
        # Update macro module's scope multipliers
        import macro
        for scope, value in self.ScopeData.items():
            macro.scope_multipliers[scope] = float(value)
            
        self.added = False

    def on_crouch_pressed(self):
        """Обработчик нажатия клавиши приседания"""
        self.crouch_pressed = True
        self.Current_posture = "c"
        macro.recalculate_recoil()

    def on_crouch_released(self):
        """Обработчик отпускания клавиши приседания"""
        self.crouch_pressed = False
        self.print_debug("Crouch released, resetting recoil")
    def get_window_version(self):
        windows_version = sys.getwindowsversion()
        if windows_version.build >= 22000:
            return True
        return False

    def get_guns_result(self):
        """
        获取枪械识别结果
        :return:
        """
        return [self._Result1, self._Result2]

    def recognize_all_guns_info(self, Emit):
        """
        Gun attachment recognition
        """
        print("Starting weapon recognition...")
        # Gunakan cara yang sama dengan script contoh
        Data = asyncio.run(capture_all_positions_thread(self.Monitor))
        self._Result1 = Data[0]
        self._Result2 = Data[1]

        print(f"Recognition results - Slot 1: {self._Result1}")
        print(f"Recognition results - Slot 2: {self._Result2}")

        if self._Result1:
            print("Processing slot 1 data...")
            self.process_gun_data(self._Result1, "1", Emit)
        else:
            print("No data for slot 1")

        if self._Result2:
            print("Processing slot 2 data...")
            self.process_gun_data(self._Result2, "2", Emit)
        else:
            print("No data for slot 2")

        Emit('g', (None,))

    def process_gun_data(self, gun_data, gun_num, Emit):
        """Process gun data"""
        print(f"Processing gun data for slot {gun_num}: {gun_data}")

        if not gun_data or not (gun_name := gun_data.get("Name")):
            print(f"No valid gun data for slot {gun_num}")
            return

        if gun_name != "None":
            gun_data_dict = {
                "name": gun_name,
                "scope": gun_data.get("Scope", "None"),
                "grip": gun_data.get("Grip", "None"),
                "stock": gun_data.get("Stock", "None"),
                "muzzle": gun_data.get("Muzzle", "None")
            }

            print(f"Slot {gun_num} weapon data: {gun_data_dict}")

            if gun_num == "1":
                self.gun1_data = gun_data_dict
                print(f"Updated gun1_data: {self.gun1_data}")
            else:
                self.gun2_data = gun_data_dict
                print(f"Updated gun2_data: {self.gun2_data}")

            if str(self.Current_firearms) == gun_num:
                print(f"Current weapon is slot {gun_num}, applying settings...")
                self.apply_gun_settings(gun_data_dict)
            else:
                print(f"Current weapon is slot {self.Current_firearms}, not applying settings for slot {gun_num}")
        else:
            print(f"No weapon detected in slot {gun_num}")

    def apply_gun_settings(self, gun_data):
        print(f"Applying gun settings: {gun_data}")
        macro.set_current_weapon_by_detected_name(gun_data["name"])
        print(f"Set weapon in macro: {gun_data['name']}")
        macro.set_current_attachments(
            gun_data["scope"],
            gun_data["grip"],
            gun_data["stock"],
            gun_data["muzzle"]
        )
        print(f"Set attachments in macro: scope={gun_data['scope']}, grip={gun_data['grip']}, stock={gun_data['stock']}, muzzle={gun_data['muzzle']}")

        # Verify the macro state was updated correctly
        print(f"Macro current weapon: {macro.current_weapon}")
        print(f"Macro current attachments: {macro.current_attachments}")
        print(f"Macro adjusted_down: {macro.adjusted_down}")

    def calculate_gun_recoil(self, gun_data):
        """Расчет отдачи для оружия"""
        if not gun_data or gun_data["name"].lower() in ['none', 'unknown', '未知']:
            return "---"
            
        return macro.calculate_final_adjusted_down(
            gun_data["name"],
            gun_data["scope"],
            gun_data["grip"],
            gun_data["stock"],
            gun_data["muzzle"],
            self.Current_posture == "c",
            self.shift_pressed
        )

    def Change_firearms(self, keyWord):
        """
        Переключение оружия с учетом режима прослушивания
        """
        print(f"Switching to weapon slot: {keyWord}")
        print(f"Current listen mode: {self.listen_mode}")
        print(f"Gun1 data: {self.gun1_data}")
        print(f"Gun2 data: {self.gun2_data}")

        if keyWord in ["1", "2"]:
            old_firearms = self.Current_firearms
            self.Current_firearms = int(keyWord)
            gun_data = self.gun1_data if keyWord == "1" else self.gun2_data

            print(f"Switched from slot {old_firearms} to slot {self.Current_firearms}")
            print(f"Selected gun data: {gun_data}")

            if self.listen_mode != "both" and keyWord != self.listen_mode:
                print(f"Slot {keyWord} not being tracked (listen mode: {self.listen_mode})")
                macro.set_current_weapon_by_detected_name("none")
                return "Weapon slot not being tracked"

            if gun_data and gun_data["name"].lower() not in ['none', 'unknown', '未知']:
                print(f"Setting up weapon: {gun_data['name']} with attachments")

                # Set weapon and attachments with explicit logging
                macro.set_current_weapon_by_detected_name(gun_data["name"])
                print(f"Weapon set in macro: {gun_data['name']}")

                macro.set_current_attachments(
                    gun_data["scope"],
                    gun_data["grip"],
                    gun_data["stock"],
                    gun_data["muzzle"]
                )
                print(f"Attachments set: scope={gun_data['scope']}, grip={gun_data['grip']}, stock={gun_data['stock']}, muzzle={gun_data['muzzle']}")

                # Get current key states for proper recoil calculation
                ctrl_pressed = win32api.GetKeyState(win32con.VK_CONTROL) < 0 or win32api.GetKeyState(0xA2) < 0 or win32api.GetKeyState(0xA3) < 0
                shift_pressed = win32api.GetKeyState(win32con.VK_SHIFT) < 0 or win32api.GetKeyState(0xA0) < 0 or win32api.GetKeyState(0xA1) < 0

                # Update internal state to match key states
                if ctrl_pressed:
                    self.Current_posture = "c"
                    self.crouch_pressed = True
                    print(f"Weapon switch while crouching: {gun_data['name']}")

                if shift_pressed:
                    self.shift_pressed = True
                    print(f"Weapon switch while running: {gun_data['name']}")

                # Force macro recalculation to ensure proper state
                macro.recalculate_recoil()
                print("Forced macro recoil recalculation")

                final_recoil = macro.calculate_final_adjusted_down(
                    gun_data["name"],
                    gun_data["scope"],
                    gun_data["grip"],
                    gun_data["stock"],
                    gun_data["muzzle"],
                    self.Current_posture == "c",
                    self.shift_pressed
                )
                print(f"Final calculated recoil: {final_recoil}")

                if self.listen_mode == "both" or self.listen_mode == keyWord:
                    return f"Switched to {gun_data['name']}, calculated recoil: {final_recoil}"
                return None
            else:
                print(f"No valid weapon data for slot {keyWord}")
                macro.set_current_weapon_by_detected_name("none")
                if self.listen_mode == "both" or self.listen_mode == keyWord:
                    return "No weapon detected in this slot"
                return None
        else:
            print("Removing weapon")
            self.Current_firearms = None
            macro.set_current_weapon_by_detected_name("none")
            return "Weapon removed"

    def IF_Open_Lens(self):
        self.StartFire = recogniseif_firearm(self.Monitor)

    def Change_posture(self, keyWord):
        """
        Изменение стойки
        """
        old_posture = self.Current_posture
        if keyWord == "space" or self.Current_posture == keyWord:
            self.Current_posture = "None"
        else:
            self.Current_posture = keyWord
        self.print_debug(f"Posture changed from {old_posture} to {self.Current_posture}")

    def calculate_scope_multiplier(scope):
        scope_multipliers = {
            "none": 1.0,
            "hongdian": 1.0,
            "quanxi": 1.0,
            "2bei": 2.0,
            "3bei": 3.0,
            "4bei": 4.0,
            "6bei": 6.0,
            "8bei": 8.0,
            "15bei": 15.0
        }
        return scope_multipliers.get(scope.lower(), 1.0)

    def calculate_attachment_multiplier(attachment):
        attachment_multipliers = {
            "none": 1.0,
            "default": 1.0
        }
        return attachment_multipliers.get(attachment.lower(), 1.0)

    def get_guns_info(self):
        """
        Получение информации о текущем оружии с учетом режима прослушивания
        """
        if self.Current_firearms == 1:
            gun_info = self.gun1_data
        elif self.Current_firearms == 2:
            gun_info = self.gun2_data
        else:
            return None

        if (self.listen_mode != "both" and 
            str(self.Current_firearms) != self.listen_mode):
            return {
                "Name": "none",
                "Scope": "none",
                "Grip": "none",
                "Stock": "none",
                "Muzzle": "none"
            }

        if gun_info:
            gun_name = gun_info.get("name", None)
            if gun_name:
                return {
                    "Name": gun_name,
                    "Scope": gun_info["scope"],
                    "Grip": gun_info["grip"],
                    "Stock": gun_info["stock"],
                    "Muzzle": gun_info["muzzle"]
                }
        return None

    def FIRE_Start(self, Emit):

        Judge_List = (self.StartFire, self.Current_firearms, self._Result1, self._Result2)
        LogInfo_List = ("当前没有开启倍镜，无需压枪", "当前没有装备枪械，无需压枪", "还未进行枪械识别，无需压枪",
                        "还未进行枪械识别，无需压枪")
        for i in range(len(Judge_List)):
            if not Judge_List[i]:
                return Emit("l", (LogInfo_List[i],))

        guns_info = self.get_guns_info()
        if not guns_info:
            return Emit("l", ("Оружие не экипировано, компенсация отдачи отключена",))

        gunsName = guns_info.get("Name", "None")
        if gunsName == "None" or not gunsName:
            return Emit("l", ("Оружие не распознано, компенсация отдачи отключена",))

        gun = self.read_gun_data(gunsName)
        if not gun:
            return Emit("l", ("枪械数据不存在",))

        NameCode = self.get_accessories_nameCode(guns_info)
        self.print_debug(f"NameCode: {NameCode}")

        ballistic = gun.get(NameCode, [])

        Posture = gun.get(self.Current_posture.lower(), 1)

        accessor_scope = guns_info.get("Scope", "None").lower()
        Scope = self.ScopeData.get(accessor_scope, 1)

        grip = guns_info.get("Grip", "None").lower()
        stock = guns_info.get("Stock", "None").lower()
        muzzle = guns_info.get("Muzzle", "None").lower()

        Not_Guns = ["sks", "mini14", "delagongnuofu", "m16a4", "mk12", "mk47", "qbu", "zidongzhuangtianbuqiang"]
        if gunsName in Not_Guns:
            return self.FIRE1(Posture, Scope, ballistic, grip, stock, muzzle, Emit)
        else:
            return self.FIRE(Posture, Scope, ballistic, grip, stock, muzzle, Emit)

    def get_accessories_nameCode(self, guns_info):
        NameCode = ""
        type_dict = {"Muzzle": "A", "Grip": "B", "Stock": "C"}
        for name, value in type_dict.items():
            accessories = guns_info.get(name, "None").lower()
            code_num = KEY_DATA[name][accessories]
            NameCode += value + code_num
        return NameCode

    def Computation_latency(self, latency):
        if self.window_version:
            return (latency - 0) / 1000
        return latency / 1000

    def FIRE(self, posture, scope, ballistic, grip, stock, muzzle, Emit):
        """
        Improved firing function with recoil compensation
        """
        recoil_list = []
        for recoil in ballistic:
            if not self.mouse_one:
                break
                
            Emit('x', (True,))
            final_recoil = self.calculate_the_recoil(recoil, posture, scope, grip, stock, muzzle)
            recoil_list.append(final_recoil)

            self.move_mouse(0, final_recoil)

            time.sleep(self.Computation_latency(9))
                
        Emit('x', (False,))
        return recoil_list

    def FIRE1(self, posture, scope, ballistic, grip, stock, muzzle, Emit):
        recoil_list = []
        for i in ballistic:
            if not self.mouse_one:
                break
            Emit('x', (True,))
            recoil = self.calculate_the_recoil(i, posture, scope, grip, stock, muzzle)
            recoil_list.append(recoil)
            self.op.MoveR(0, recoil)
            latency = self.Computation_latency(100)
            time.sleep(latency)
        Emit('x', (False,))
        return recoil_list

    def calculate_the_recoil(self, recoil, posture, scope, grip, stock, muzzle):
        return macro.calculate_recoil_with_state(
            recoil,
            self.GunsName,
            scope,
            grip, 
            stock,
            muzzle,
            self.Current_posture,
            self.shift_pressed
        )

    def set_crouch_key(self, key):
        """
        Устанавливает клавишу для приседания
        """
        key_mapping = {
            "CTRL": "ctrl_l",
            "ALT": "alt_l",
            "C": "c"
        }
        self.crouch_key = key_mapping.get(key, "ctrl_l")
        self.print_debug(f"Crouch key set to: {self.crouch_key}")

    def set_crouch_toggle_mode(self, enabled):
        """Установка режима приседания"""
        self.crouch_toggle_mode = enabled
        self.is_crouched = False
        self.save_config_data('crouch_toggle', enabled)
        
    def on_crouch_key_event(self, pressed):
        """Обработка нажатия клавиши приседа с учетом режима"""
        if self.crouch_toggle_mode:
            if pressed:
                self.is_crouched = not self.is_crouched
                if self.is_crouched:
                    self.Current_posture = "c"
                    self.on_crouch_pressed()
                else:
                    self.Current_posture = "None"
                    self.on_crouch_released()
        else:
            self.is_crouched = pressed
            if pressed:
                self.Current_posture = "c"
                self.on_crouch_pressed()
            else:
                self.Current_posture = "None"
                self.on_crouch_released()

    def show_overlay(self):
        """Безопасный показ оверлея"""
        try:
            # Save current scope data before creating overlay
            current_scope_data = {k: v for k, v in self.ScopeData.items()}
            print("Current scope data before creating overlay:")
            for scope, value in current_scope_data.items():
                print(f"  - {scope}: {value}")
                
            if not self.overlay:
                from overlay import InfoOverlay
                app = QApplication.instance()
                if not app:
                    app = QApplication([])
                
                # Make sure to reload scope configuration before creating overlay
                self.reload_scope_config()
                
                self.overlay = InfoOverlay()
                self.overlay.set_process(self)

                try:
                    # Cek resolusi layar
                    screen = QApplication.primaryScreen().geometry()
                    screen_width = screen.width()
                    screen_height = screen.height()
                    
                    # Baca posisi dari file konfigurasi
                    with open('./Config/overlay_position.json', 'r') as f:
                        pos = json.load(f)
                        x_pos = pos.get('x', 1700)
                        y_pos = pos.get('y', 10)
                        
                    # Pastikan posisi berada dalam area yang terlihat
                    if x_pos > screen_width - 100:
                        x_pos = screen_width - 220
                    if y_pos > screen_height - 100:
                        y_pos = 10
                        
                    self.overlay.move(x_pos, y_pos)
                    print(f"Positioned overlay at {x_pos}, {y_pos}, screen size: {screen_width}x{screen_height}")
                except Exception as e:
                    print(f"Error loading overlay position: {e}")
                    # Posisi default yang aman untuk 1920x1080
                    screen = QApplication.primaryScreen().geometry()
                    x_pos = min(screen.width() - 220, 1700)
                    self.overlay.move(x_pos, 10)
                    print(f"Using safe default position: {x_pos}, 10")

                opacity = self.get_config_data('overlay_opacity') or 50
                self.overlay.setWindowOpacity(opacity / 100.0)
                
                # Verify scope data hasn't been changed
                print("Verifying scope data after overlay creation:")
                for scope, value in current_scope_data.items():
                    if scope in self.ScopeData and self.ScopeData[scope] != value:
                        print(f"  ! Scope value changed: {scope}: {value} -> {self.ScopeData[scope]}, restoring")
                        self.ScopeData[scope] = value
                
            # Ensure overlay is shown
            if self.overlay_enabled:
                # Reload scope config again to ensure it's up to date
                self.reload_scope_config()
                
                # Double-check that 8x scope value is correct
                if '8x' in self.ScopeData and self.ScopeData['8x'] != current_scope_data.get('8x', 8.0):
                    print(f"Fixing 8x scope value: {self.ScopeData['8x']} -> {current_scope_data.get('8x', 8.0)}")
                    self.ScopeData['8x'] = current_scope_data.get('8x', 8.0)
                if '8bei' in self.ScopeData and self.ScopeData['8bei'] != current_scope_data.get('8bei', 8.0):
                    print(f"Fixing 8bei scope value: {self.ScopeData['8bei']} -> {current_scope_data.get('8bei', 8.0)}")
                    self.ScopeData['8bei'] = current_scope_data.get('8bei', 8.0)
                
                self.update_overlay_info()
                self.overlay.show()
                print("Overlay shown successfully")
                
                # Final verification of scope values
                print("Final scope values after showing overlay:")
                for scope in ['8x', '8bei', '4x', '4bei']:
                    if scope in self.ScopeData:
                        print(f"  - {scope}: {self.ScopeData[scope]}")
                
        except Exception as e:
            print(f"Error showing overlay: {e}")
            import traceback
            traceback.print_exc()
            self.overlay_enabled = False
            self.save_config_data('overlay', False)

    def update_overlay_info(self):
        """Обновление информации в оверлее"""
        if self.overlay and self.overlay.isVisible():
            try:
                import macro
                weapon1 = self.gun1_data.get("name", "No weapon") if self.gun1_data else "No weapon"
                weapon2 = self.gun2_data.get("name", "No weapon") if self.gun2_data else "No weapon"

                if self.gun1_data:
                    recoil1 = macro.calculate_final_adjusted_down(
                        weapon1,
                        self.gun1_data.get("scope", "none"),
                        self.gun1_data.get("grip", "none"),
                        self.gun1_data.get("stock", "none"),
                        self.gun1_data.get("muzzle", "none"),
                        self.crouch_pressed,
                        self.shift_pressed
                    )
                else:
                    recoil1 = "---"
                    
                if self.gun2_data:
                    recoil2 = macro.calculate_final_adjusted_down(
                        weapon2,
                        self.gun2_data.get("scope", "none"),
                        self.gun2_data.get("grip", "none"),
                        self.gun2_data.get("stock", "none"),
                        self.gun2_data.get("muzzle", "none"),
                        self.crouch_pressed,
                        self.shift_pressed
                    )
                else:
                    recoil2 = "---"

                current_slot = str(self.Current_firearms) if self.Current_firearms else "-"
                
                # Get tab status from macro
                tab_disabled = macro.disabled_tab.is_set()
                
                self.overlay.update_info(
                    weapon1=weapon1,
                    weapon2=weapon2,
                    macro_status=macro.macro_enabled.is_set(),
                    tab_status=tab_disabled,
                    recoil1=recoil1,
                    recoil2=recoil2,
                    stance="Crouching" if self.crouch_pressed else "Standing",
                    listen_mode=self.listen_mode,
                    current_slot=current_slot
                )
                
            except Exception as e:
                print(f"Error updating overlay info: {e}")
                import traceback
                traceback.print_exc()

    def hide_overlay(self):
        """Безопасное скрытие оверлея"""
        try:
            if self.overlay:
                print("Hiding overlay...")
                self.overlay.hide()
                self.overlay.close()
                self.overlay = None
                print("Overlay hidden successfully")
        except Exception as e:
            print(f"Error hiding overlay: {e}")
            import traceback
            traceback.print_exc()
            self.overlay_enabled = False
            self.save_config_data('overlay', False)

    def print_debug(self, message):
        """Вывод отладочной информации только если включен режим отладки"""
        if self.debug:
            print(message)

    def reload_scope_config(self):
        """Перезагрузка конфигурации прицелов"""
        try:
            print("Starting scope configuration reload...")
            
            # Save current scope values before reloading
            current_8x = self.ScopeData.get('8x', 8.0)
            current_8bei = self.ScopeData.get('8bei', 8.0)
            print(f"Current 8x value before reload: {current_8x}")
            print(f"Current 8bei value before reload: {current_8bei}")
            
            scope_data = self.get_config_data('s')
            if isinstance(scope_data, dict) and scope_data:
                print("Reloading scope configuration:")
                for scope, value in scope_data.items():
                    print(f"  - {scope}: {value}")
                
                # Store original values for debugging
                original_values = {k: v for k, v in self.ScopeData.items()}
                
                # Update scope data
                self.ScopeData.update(scope_data)
                
                # Log changes for debugging
                for scope, value in self.ScopeData.items():
                    if scope in original_values and original_values[scope] != value:
                        print(f"  * Changed {scope}: {original_values[scope]} -> {value}")
                
                # Update macro module as well
                import macro
                for scope, value in scope_data.items():
                    macro.scope_multipliers[scope] = float(value)
                    # Also update the x format if it's a bei format
                    if 'bei' in scope:
                        x_format = scope.replace('bei', 'x')
                        macro.scope_multipliers[x_format] = float(value)
                        print(f"  * Updated both {scope} and {x_format} to {value}")
                
                # Preserve the 8x scope value if it was customized
                if current_8x != 8.0:
                    print(f"Preserving custom 8x value: {current_8x}")
                    self.ScopeData['8x'] = current_8x
                    macro.scope_multipliers['8x'] = current_8x
                
                if current_8bei != 8.0:
                    print(f"Preserving custom 8bei value: {current_8bei}")
                    self.ScopeData['8bei'] = current_8bei
                    macro.scope_multipliers['8bei'] = current_8bei
                
                # Force recalculation with new values
                macro.recalculate_recoil()
                print("Scope configuration reloaded successfully")
                
                # Verify final values
                print(f"Final 8x value after reload: {self.ScopeData.get('8x', 'not set')}")
                print(f"Final 8bei value after reload: {self.ScopeData.get('8bei', 'not set')}")
                
                return True
            else:
                print(f"No valid scope data found in config: {scope_data}")
                return False
        except Exception as e:
            print(f"Error reloading scope config: {e}")
            import traceback
            traceback.print_exc()
            return False
