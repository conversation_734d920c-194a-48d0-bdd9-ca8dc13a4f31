#!/usr/bin/env python3
"""
Test script to validate the muzzle detection fixes for slot 2.
This script tests the enhanced muzzle detection logic.
"""

import cv2
import numpy as np
import os
from recognition import match_sift, template_match_fallback

def test_enhanced_sift_matching():
    """Test the enhanced SIFT matching with muzzle-specific optimizations"""
    print("Testing enhanced SIFT matching for muzzle detection...")
    print("=" * 60)
    
    # Create test images
    test_img1 = np.random.randint(0, 255, (50, 50), dtype=np.uint8)
    test_img2 = np.random.randint(0, 255, (50, 50), dtype=np.uint8)
    
    # Test with different component types
    score_normal = match_sift(test_img1, test_img2, "Name")
    score_muzzle = match_sift(test_img1, test_img2, "Muzzle")
    
    print(f"Normal component matching score: {score_normal:.4f}")
    print(f"Muzzle component matching score: {score_muzzle:.4f}")
    
    # The muzzle matching should use more lenient thresholds
    print("✓ Enhanced SIFT matching test completed")

def test_template_fallback():
    """Test the template matching fallback"""
    print("\nTesting template matching fallback...")
    print("=" * 60)
    
    # Create test images with some similarity
    test_img1 = np.ones((50, 50), dtype=np.uint8) * 128
    test_img2 = np.ones((50, 50), dtype=np.uint8) * 130  # Slightly different
    
    score = template_match_fallback(test_img1, test_img2)
    print(f"Template matching fallback score: {score:.4f}")
    
    # Test with different sizes
    test_img3 = np.ones((60, 60), dtype=np.uint8) * 128
    score2 = template_match_fallback(test_img1, test_img3)
    print(f"Template matching with size difference: {score2:.4f}")
    
    print("✓ Template matching fallback test completed")

def test_muzzle_templates():
    """Test loading and processing muzzle templates"""
    print("\nTesting muzzle template processing...")
    print("=" * 60)
    
    muzzle_path = "_internal/data/firearms/Muzzle/"
    
    if not os.path.exists(muzzle_path):
        print(f"ERROR: Muzzle template path not found: {muzzle_path}")
        return False
    
    templates = os.listdir(muzzle_path)
    print(f"Found {len(templates)} muzzle templates")
    
    valid_templates = 0
    for template in templates:
        if template.endswith('.png'):
            template_path = os.path.join(muzzle_path, template)
            img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
            
            if img is not None:
                valid_templates += 1
                print(f"  ✓ {template}: {img.shape}")
            else:
                print(f"  ✗ {template}: Failed to load")
    
    print(f"Valid templates: {valid_templates}/{len(templates)}")
    return valid_templates > 0

def create_mock_roi_test():
    """Create mock ROI images to test muzzle detection"""
    print("\nCreating mock ROI test...")
    print("=" * 60)
    
    # Create mock ROI images for testing
    mock_roi_slot1 = np.random.randint(50, 200, (48, 49), dtype=np.uint8)
    mock_roi_slot2 = np.random.randint(50, 200, (48, 49), dtype=np.uint8)
    
    # Save mock ROIs
    cv2.imwrite("mock_muzzle_slot1_roi.png", mock_roi_slot1)
    cv2.imwrite("mock_muzzle_slot2_roi.png", mock_roi_slot2)
    
    print(f"Created mock ROI for slot 1: {mock_roi_slot1.shape}")
    print(f"Created mock ROI for slot 2: {mock_roi_slot2.shape}")
    
    # Test against actual muzzle templates
    muzzle_path = "_internal/data/firearms/Muzzle/"
    if os.path.exists(muzzle_path):
        templates = [f for f in os.listdir(muzzle_path) if f.endswith('.png')]
        
        if templates:
            test_template = templates[0]
            template_path = os.path.join(muzzle_path, test_template)
            template_img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
            
            if template_img is not None:
                score1 = match_sift(mock_roi_slot1, template_img, "Muzzle")
                score2 = match_sift(mock_roi_slot2, template_img, "Muzzle")
                
                print(f"Mock slot 1 vs {test_template}: {score1:.4f}")
                print(f"Mock slot 2 vs {test_template}: {score2:.4f}")
    
    print("✓ Mock ROI test completed")

def validate_coordinate_system():
    """Validate that the coordinate system is working correctly"""
    print("\nValidating coordinate system...")
    print("=" * 60)
    
    from resolution_setting import RESOLUTION_SETTINGS, GUNS_REOLUTION_SETTINGS
    
    # Test with 1920x1080 resolution
    resolution = '1920x1080'
    if resolution in RESOLUTION_SETTINGS and resolution in GUNS_REOLUTION_SETTINGS:
        regions = RESOLUTION_SETTINGS[resolution]
        capture_region = GUNS_REOLUTION_SETTINGS[resolution]
        
        print(f"Capture region: {capture_region}")
        print(f"Muzzle_1: {regions['Muzzle_1']}")
        print(f"Muzzle_2: {regions['Muzzle_2']}")
        
        # Check if muzzle regions are within the capture dimensions
        cap_width, cap_height = capture_region[2], capture_region[3]
        
        for slot in ['1', '2']:
            muzzle_key = f'Muzzle_{slot}'
            if muzzle_key in regions:
                x1, y1, x2, y2 = regions[muzzle_key]
                
                if x2 <= cap_width and y2 <= cap_height:
                    print(f"✓ {muzzle_key} is within capture dimensions")
                else:
                    print(f"✗ {muzzle_key} exceeds capture dimensions!")
                    print(f"  Region: ({x1}, {y1}) to ({x2}, {y2})")
                    print(f"  Capture: {cap_width}x{cap_height}")
    
    print("✓ Coordinate system validation completed")

def print_fix_summary():
    """Print summary of muzzle detection fixes"""
    print("\n" + "=" * 60)
    print("MUZZLE DETECTION FIXES SUMMARY")
    print("=" * 60)
    
    fixes = [
        "1. Enhanced SIFT matching for muzzle attachments:",
        "   - More lenient distance threshold (0.8 vs 0.7)",
        "   - Component-specific optimization",
        "",
        "2. Added template matching fallback:",
        "   - For cases where SIFT fails due to few keypoints",
        "   - Uses normalized cross-correlation",
        "",
        "3. Comprehensive muzzle debugging:",
        "   - ROI statistics and shape logging",
        "   - Individual template matching scores",
        "   - Debug image saving for manual inspection",
        "",
        "4. Enhanced validation in Process.py:",
        "   - Special warnings for missing muzzle attachments",
        "   - Success confirmation when muzzle is detected",
        "",
        "5. Improved error handling:",
        "   - Better template loading validation",
        "   - Graceful fallback for edge cases"
    ]
    
    for fix in fixes:
        print(fix)

def main():
    """Main test function"""
    print("PUBG Muzzle Detection Fix Validation")
    print("=" * 60)
    
    # Run all tests
    test_enhanced_sift_matching()
    test_template_fallback()
    test_muzzle_templates()
    create_mock_roi_test()
    validate_coordinate_system()
    
    print_fix_summary()
    
    print("\n" + "=" * 60)
    print("TESTING RECOMMENDATIONS")
    print("=" * 60)
    
    recommendations = [
        "1. Run the main application and press TAB to trigger weapon recognition",
        "2. Look for 'MUZZLE DEBUG' messages in the console output",
        "3. Check for 'WARNING: No muzzle attachment detected' messages",
        "4. Verify that debug ROI images are saved (debug_muzzle_slot1_roi.png, etc.)",
        "5. Test with both weapon slots to compare muzzle detection",
        "6. Monitor the confidence scores for muzzle attachments",
        "7. If issues persist, check the saved debug ROI images manually"
    ]
    
    for rec in recommendations:
        print(rec)

if __name__ == "__main__":
    main()
