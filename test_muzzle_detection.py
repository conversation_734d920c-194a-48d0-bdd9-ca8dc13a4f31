#!/usr/bin/env python3
"""
Diagnostic script to test muzzle detection specifically for slot 2 vs slot 1.
This script helps identify why muzzle attachments aren't being detected for slot 2.
"""

import cv2
import os
import numpy as np
from resolution_setting import RESOLUTION_SETTINGS, GUNS_REOLUTION_SETTINGS
from recognition import MSS_Img, match_sift

def test_muzzle_regions():
    """Test muzzle region coordinates for all resolutions"""
    print("Testing muzzle region coordinates...")
    print("=" * 60)
    
    for resolution, regions in RESOLUTION_SETTINGS.items():
        print(f"\nResolution: {resolution}")
        
        muzzle_1 = regions.get('Muzzle_1')
        muzzle_2 = regions.get('Muzzle_2')
        
        if not muzzle_1 or not muzzle_2:
            print(f"  ERROR: Missing muzzle regions for {resolution}")
            continue
            
        print(f"  Muzzle_1: {muzzle_1}")
        print(f"  Muzzle_2: {muzzle_2}")
        
        # Calculate region sizes
        m1_width = muzzle_1[2] - muzzle_1[0]
        m1_height = muzzle_1[3] - muzzle_1[1]
        m2_width = muzzle_2[2] - muzzle_2[0]
        m2_height = muzzle_2[3] - muzzle_2[1]
        
        print(f"  Muzzle_1 size: {m1_width}x{m1_height}")
        print(f"  Muzzle_2 size: {m2_width}x{m2_height}")
        
        # Check if sizes are similar (they should be)
        if abs(m1_width - m2_width) > 5 or abs(m1_height - m2_height) > 5:
            print(f"  WARNING: Significant size difference between slot 1 and 2 muzzle regions!")
        else:
            print(f"  ✓ Muzzle region sizes are consistent")

def test_muzzle_templates():
    """Test muzzle template files"""
    print("\n\nTesting muzzle template files...")
    print("=" * 60)
    
    muzzle_path = "_internal/data/firearms/Muzzle/"
    
    if not os.path.exists(muzzle_path):
        print(f"ERROR: Muzzle template path does not exist: {muzzle_path}")
        return False
    
    templates = os.listdir(muzzle_path)
    print(f"Found {len(templates)} muzzle templates:")
    
    for template in templates:
        template_path = os.path.join(muzzle_path, template)
        img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
        
        if img is None:
            print(f"  ERROR: Could not load {template}")
        else:
            height, width = img.shape
            print(f"  ✓ {template}: {width}x{height}")
    
    return True

def simulate_muzzle_detection(resolution='1920x1080'):
    """Simulate muzzle detection for both slots"""
    print(f"\n\nSimulating muzzle detection for {resolution}...")
    print("=" * 60)
    
    if resolution not in RESOLUTION_SETTINGS:
        print(f"ERROR: Resolution {resolution} not found")
        return
    
    regions = RESOLUTION_SETTINGS[resolution]
    full_capture_region = GUNS_REOLUTION_SETTINGS[resolution]
    
    print(f"Full capture region: {full_capture_region}")
    print(f"Muzzle_1 region: {regions['Muzzle_1']}")
    print(f"Muzzle_2 region: {regions['Muzzle_2']}")
    
    # Check if muzzle regions are within the full capture area
    def check_region_validity(region_name, region_coords):
        x1, y1, x2, y2 = region_coords
        cap_x1, cap_y1, cap_w, cap_h = full_capture_region
        cap_x2 = cap_x1 + cap_w
        cap_y2 = cap_y1 + cap_h
        
        print(f"\n{region_name} analysis:")
        print(f"  Region coords: ({x1}, {y1}) to ({x2}, {y2})")
        print(f"  Capture area: ({cap_x1}, {cap_y1}) to ({cap_x2}, {cap_y2})")
        
        # Check if region is within capture area
        if x1 < cap_x1 or y1 < cap_y1 or x2 > cap_x2 or y2 > cap_y2:
            print(f"  ERROR: {region_name} is outside capture area!")
            return False
        else:
            print(f"  ✓ {region_name} is within capture area")
            return True
    
    muzzle_1_valid = check_region_validity("Muzzle_1", regions['Muzzle_1'])
    muzzle_2_valid = check_region_validity("Muzzle_2", regions['Muzzle_2'])
    
    return muzzle_1_valid and muzzle_2_valid

def analyze_coordinate_patterns():
    """Analyze coordinate patterns to identify potential issues"""
    print("\n\nAnalyzing coordinate patterns...")
    print("=" * 60)
    
    for resolution, regions in RESOLUTION_SETTINGS.items():
        print(f"\n{resolution}:")
        
        # Get all slot 1 and slot 2 regions
        slot1_regions = {k: v for k, v in regions.items() if k.endswith('_1')}
        slot2_regions = {k: v for k, v in regions.items() if k.endswith('_2')}
        
        print(f"  Slot 1 regions: {len(slot1_regions)}")
        print(f"  Slot 2 regions: {len(slot2_regions)}")
        
        # Check muzzle specifically
        if 'Muzzle_1' in slot1_regions and 'Muzzle_2' in slot2_regions:
            m1 = slot1_regions['Muzzle_1']
            m2 = slot2_regions['Muzzle_2']
            
            # Calculate vertical offset between slots
            vertical_offset = m2[1] - m1[1]  # y1 difference
            print(f"  Muzzle vertical offset (slot2 - slot1): {vertical_offset}")
            
            # Check if horizontal positions are similar
            horizontal_diff = abs(m2[0] - m1[0])  # x1 difference
            print(f"  Muzzle horizontal difference: {horizontal_diff}")
            
            if horizontal_diff > 10:
                print(f"  WARNING: Large horizontal difference in muzzle positions!")

def create_debug_capture_function():
    """Create an enhanced capture function with muzzle-specific debugging"""
    print("\n\nCreating debug capture function...")
    print("=" * 60)
    
    debug_code = '''
async def debug_capture_muzzle(current_res):
    """Debug version of capture function focused on muzzle detection"""
    from recognition import MSS_Img, capture_and_compare, match_sift
    import os
    import cv2
    
    Guns_imgs = GUNS_REOLUTION_SETTINGS[current_res]
    Guns_img = RESOLUTION_SETTINGS[current_res]
    
    print(f"Debug: Capturing full image with region {Guns_imgs}")
    Images = MSS_Img(Guns_imgs)
    print(f"Debug: Captured image shape: {Images.shape}")
    
    # Focus on muzzle regions only
    muzzle_regions = {k: v for k, v in Guns_img.items() if 'Muzzle' in k}
    print(f"Debug: Muzzle regions to process: {list(muzzle_regions.keys())}")
    
    for region_name, coords in muzzle_regions.items():
        x1, y1, x2, y2 = coords
        roi = Images[y1:y2, x1:x2]
        
        print(f"Debug: {region_name} ROI shape: {roi.shape}")
        print(f"Debug: {region_name} ROI stats - min: {roi.min()}, max: {roi.max()}, mean: {roi.mean():.2f}")
        
        # Save ROI for manual inspection
        cv2.imwrite(f"debug_{region_name}_{current_res}.png", roi)
        print(f"Debug: Saved {region_name} ROI to debug_{region_name}_{current_res}.png")
        
        # Test against muzzle templates
        muzzle_path = "_internal/data/firearms/Muzzle/"
        if os.path.exists(muzzle_path):
            templates = os.listdir(muzzle_path)
            print(f"Debug: Testing {region_name} against {len(templates)} templates")
            
            best_match = ""
            best_score = 0.0
            
            for template in templates:
                template_path = os.path.join(muzzle_path, template)
                template_img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
                
                if template_img is not None:
                    score = match_sift(roi, template_img)
                    print(f"Debug: {region_name} vs {template}: {score:.4f}")
                    
                    if score > best_score:
                        best_score = score
                        best_match = template[:-4]  # Remove .png extension
            
            print(f"Debug: {region_name} best match: '{best_match}' (score: {best_score:.4f})")
    '''
    
    print("Debug function created. You can add this to recognition.py for detailed muzzle debugging.")
    return debug_code

def main():
    """Main diagnostic function"""
    print("PUBG Muzzle Detection Diagnostic Tool")
    print("=" * 60)
    
    # Run all tests
    test_muzzle_regions()
    test_muzzle_templates()
    simulate_muzzle_detection()
    analyze_coordinate_patterns()
    create_debug_capture_function()
    
    print("\n\n" + "=" * 60)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    recommendations = [
        "1. Check if muzzle regions for slot 2 are correctly positioned",
        "2. Verify that muzzle template images are loading properly",
        "3. Compare SIFT matching scores between slot 1 and slot 2",
        "4. Ensure capture regions include both muzzle areas",
        "5. Test with actual game screenshots to verify coordinates",
        "6. Add the debug capture function to get detailed matching data"
    ]
    
    for rec in recommendations:
        print(rec)

if __name__ == "__main__":
    main()
