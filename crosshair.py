RESOLUTION_POSITIONS = {
    "1920x1080": (960, 540),
    "2560x1440": (1280, 720),
    "3440x1440": (1720, 720),
    "2560x1080": (1280, 540),
    "1728x1080": (864, 540),
    "3840x2160": (1920, 1080)
}

from PyQt5.QtWidgets import QWidget, QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPainter, QPen, QColor
import win32gui
import win32con
import math

class Crosshair(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(
            Qt.FramelessWindowHint | 
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)

        self.resize(100, 100)

        self.crosshair_type = "simple"
        self.crosshair_color = "white"
        self.crosshair_size = 1.0
        self.rotation_angle = 0
        self.rainbow_hue = 0

        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(16)

        screen = QApplication.primaryScreen().geometry()
        window_size = self.size()
        x = (screen.width() - window_size.width()) // 2
        y = (screen.height() - window_size.height()) // 2
        self.move(x, y)
        print(f"Screen size: {screen.width()}x{screen.height()}")
        print(f"Window size: {window_size.width()}x{window_size.height()}")
        print(f"Position: {x},{y}")

        self.setWindowFlags(self.windowFlags() | Qt.WindowTransparentForInput)
        hwnd = self.winId().__int__()
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
        win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, style | win32con.WS_EX_TRANSPARENT)

        self.current_resolution = "1920x1080"

    def update_animation(self):
        """Обновление анимации прицела"""
        if self.crosshair_type in ["swastika", "rainbow_swastika"]:
            self.rotation_angle = (self.rotation_angle + 2) % 360
            
            if self.crosshair_type == "rainbow_swastika":
                self.rainbow_hue = (self.rainbow_hue + 4) % 360
                self.update()
            else:
                self.update()

    def set_crosshair_size(self, size):
        """Установка размера прицела"""
        self.crosshair_size = float(size)
        self.update()

    def set_crosshair_type(self, type_name):
        self.crosshair_type = type_name
        self.update()

    def set_crosshair_color(self, color):
        """Установка цвета прицела"""
        self.crosshair_color = color
        self.update()
        
    def set_resolution(self, resolution):
        """Установка позиции прицела в зависимости от разрешения"""
        try:
            self.current_resolution = resolution

            width, height = map(int, resolution.split('x'))

            center_x = width // 2
            center_y = height // 2

            crosshair_width = self.width()
            crosshair_height = self.height()

            x = center_x - (crosshair_width // 2)
            y = center_y - (crosshair_height // 2)

            self.move(x, y)
            print(f"Crosshair centered for {resolution}: window={crosshair_width}x{crosshair_height}, "
                  f"screen={width}x{height}, pos={x},{y}")
                  
        except Exception as e:
            print(f"Error positioning crosshair: {e}")
            x = 960 - (self.width() // 2)
            y = 540 - (self.height() // 2)
            self.move(x, y)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        window_center = self.rect().center()

        base_size = int(10 * self.crosshair_size)
        
        if self.crosshair_type == "simple":
            color_map = {
                "white": Qt.white,
                "red": Qt.red,
                "green": Qt.green,
                "blue": Qt.blue
            }
            color = color_map.get(self.crosshair_color, Qt.white)
            painter.setPen(QPen(color, 1, Qt.SolidLine))

            painter.drawLine(window_center.x() - base_size, window_center.y(), 
                           window_center.x() + base_size, window_center.y())
            painter.drawLine(window_center.x(), window_center.y() - base_size,
                           window_center.x(), window_center.y() + base_size)
                           
        elif self.crosshair_type in ["swastika", "rainbow_swastika"]:
            painter.translate(window_center)
            painter.rotate(self.rotation_angle)

            if self.crosshair_type == "rainbow_swastika":
                color = QColor.fromHsv(int(self.rainbow_hue), 255, 255)
            else:
                color_map = {
                    "white": Qt.white,
                    "red": Qt.red,
                    "green": Qt.green,
                    "blue": Qt.blue
                }
                color = color_map.get(self.crosshair_color, Qt.white)
            
            painter.setPen(QPen(color, 2, Qt.SolidLine))

            half_size = int(base_size/2)
            for i in range(4):
                painter.rotate(90)
                painter.drawLine(0, 0, base_size, 0)
                painter.drawLine(base_size, 0, base_size, half_size)
