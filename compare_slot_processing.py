#!/usr/bin/env python3
"""
Detailed comparison of image processing between slot 1 and slot 2 muzzle detection.
This script identifies specific differences that cause accuracy disparities.
"""

import cv2
import numpy as np
import os
from recognition import match_sift, template_match_fallback
from resolution_setting import RESOLUTION_SETTINGS

class SlotProcessingComparator:
    def __init__(self, resolution='1920x1080'):
        self.resolution = resolution
        self.regions = RESOLUTION_SETTINGS[resolution]
        self.muzzle_templates = self._load_muzzle_templates()
        
    def _load_muzzle_templates(self):
        """Load muzzle templates with size information"""
        templates = {}
        muzzle_path = "_internal/data/firearms/Muzzle/"
        
        if os.path.exists(muzzle_path):
            for filename in os.listdir(muzzle_path):
                if filename.endswith('.png'):
                    template_path = os.path.join(muzzle_path, filename)
                    img = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        templates[filename[:-4]] = {
                            'image': img,
                            'size': img.shape,
                            'path': template_path
                        }
        return templates
    
    def analyze_roi_characteristics(self):
        """Analyze ROI characteristics that might affect detection"""
        print("Analyzing ROI characteristics...")
        print("=" * 60)
        
        muzzle_1_coords = self.regions['Muzzle_1']
        muzzle_2_coords = self.regions['Muzzle_2']
        
        # Calculate ROI properties
        roi1_props = self._calculate_roi_properties(muzzle_1_coords, "Slot 1")
        roi2_props = self._calculate_roi_properties(muzzle_2_coords, "Slot 2")
        
        # Compare properties
        print("\nComparison:")
        for prop in ['width', 'height', 'area', 'aspect_ratio']:
            val1 = roi1_props[prop]
            val2 = roi2_props[prop]
            diff = abs(val1 - val2)
            diff_pct = (diff / val1) * 100 if val1 > 0 else 0
            
            print(f"{prop:12}: Slot1={val1:6.2f}, Slot2={val2:6.2f}, Diff={diff:6.2f} ({diff_pct:5.1f}%)")
            
            if diff_pct > 5:  # More than 5% difference
                print(f"  ⚠️  Significant difference in {prop}")
        
        return roi1_props, roi2_props
    
    def _calculate_roi_properties(self, coords, slot_name):
        """Calculate ROI properties"""
        x1, y1, x2, y2 = coords
        width = x2 - x1
        height = y2 - y1
        area = width * height
        aspect_ratio = width / height if height > 0 else 0
        
        print(f"\n{slot_name} ROI:")
        print(f"  Coordinates: ({x1}, {y1}) to ({x2}, {y2})")
        print(f"  Size: {width}x{height}")
        print(f"  Area: {area} pixels")
        print(f"  Aspect ratio: {aspect_ratio:.3f}")
        
        return {
            'width': width,
            'height': height,
            'area': area,
            'aspect_ratio': aspect_ratio,
            'coords': coords
        }
    
    def analyze_template_scaling_impact(self):
        """Analyze how template scaling affects each slot differently"""
        print("\nAnalyzing template scaling impact...")
        print("=" * 60)
        
        roi1_size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                    self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        roi2_size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                    self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        print(f"ROI 1 size: {roi1_size[1]}x{roi1_size[0]}")
        print(f"ROI 2 size: {roi2_size[1]}x{roi2_size[0]}")
        
        scaling_results = {}
        
        for template_name, template_data in self.muzzle_templates.items():
            template_img = template_data['image']
            template_size = template_data['size']
            
            print(f"\nTemplate: {template_name} ({template_size[1]}x{template_size[0]})")
            
            # Test different scaling strategies
            strategies = [
                ("original", lambda img: img),
                ("scale_to_roi1", lambda img: cv2.resize(img, (roi1_size[1], roi1_size[0]))),
                ("scale_to_roi2", lambda img: cv2.resize(img, (roi2_size[1], roi2_size[0]))),
                ("scale_down_50", lambda img: cv2.resize(img, (img.shape[1]//2, img.shape[0]//2))),
                ("scale_down_75", lambda img: cv2.resize(img, (int(img.shape[1]*0.75), int(img.shape[0]*0.75))))
            ]
            
            # Create test ROIs
            test_roi1 = self._create_test_roi(roi1_size)
            test_roi2 = self._create_test_roi(roi2_size)
            
            strategy_scores = {}
            
            for strategy_name, scale_func in strategies:
                try:
                    scaled_template = scale_func(template_img)
                    
                    # Test against both ROIs
                    if scaled_template.shape[0] <= roi1_size[0] and scaled_template.shape[1] <= roi1_size[1]:
                        score1 = template_match_fallback(test_roi1, scaled_template)
                    else:
                        score1 = 0.0
                    
                    if scaled_template.shape[0] <= roi2_size[0] and scaled_template.shape[1] <= roi2_size[1]:
                        score2 = template_match_fallback(test_roi2, scaled_template)
                    else:
                        score2 = 0.0
                    
                    strategy_scores[strategy_name] = {
                        'slot1': score1,
                        'slot2': score2,
                        'difference': abs(score1 - score2)
                    }
                    
                    print(f"  {strategy_name:15}: Slot1={score1:.4f}, Slot2={score2:.4f}, Diff={abs(score1-score2):.4f}")
                    
                except Exception as e:
                    print(f"  {strategy_name:15}: Error - {e}")
            
            scaling_results[template_name] = strategy_scores
        
        return scaling_results
    
    def _create_test_roi(self, size):
        """Create a test ROI with some features"""
        roi = np.random.randint(100, 150, size, dtype=np.uint8)
        # Add some structure
        center_y, center_x = size[0]//2, size[1]//2
        cv2.circle(roi, (center_x, center_y), min(size)//4, 200, -1)
        return roi
    
    def analyze_preprocessing_pipeline(self):
        """Analyze the preprocessing pipeline for differences"""
        print("\nAnalyzing preprocessing pipeline...")
        print("=" * 60)
        
        # Create identical test images for both slots
        test_image = np.random.randint(0, 255, (400, 600), dtype=np.uint8)
        
        # Extract ROIs using the same process as the actual detection
        roi1_coords = self.regions['Muzzle_1']
        roi2_coords = self.regions['Muzzle_2']
        
        roi1 = test_image[roi1_coords[1]:roi1_coords[3], roi1_coords[0]:roi1_coords[2]]
        roi2 = test_image[roi2_coords[1]:roi2_coords[3], roi2_coords[0]:roi2_coords[2]]
        
        print(f"Extracted ROI 1 shape: {roi1.shape}")
        print(f"Extracted ROI 2 shape: {roi2.shape}")
        
        # Analyze image statistics
        stats1 = self._calculate_image_stats(roi1, "ROI 1")
        stats2 = self._calculate_image_stats(roi2, "ROI 2")
        
        # Compare statistics
        print("\nStatistical comparison:")
        for stat in ['mean', 'std', 'min', 'max']:
            val1 = stats1[stat]
            val2 = stats2[stat]
            diff = abs(val1 - val2)
            print(f"{stat:8}: ROI1={val1:7.2f}, ROI2={val2:7.2f}, Diff={diff:7.2f}")
        
        return stats1, stats2
    
    def _calculate_image_stats(self, img, name):
        """Calculate image statistics"""
        stats = {
            'mean': np.mean(img),
            'std': np.std(img),
            'min': np.min(img),
            'max': np.max(img)
        }
        
        print(f"\n{name} statistics:")
        for stat, value in stats.items():
            print(f"  {stat}: {value:.2f}")
        
        return stats
    
    def test_detection_algorithms(self):
        """Test different detection algorithms on both slots"""
        print("\nTesting detection algorithms...")
        print("=" * 60)
        
        # Create test ROIs with known patterns
        roi1_size = (self.regions['Muzzle_1'][3] - self.regions['Muzzle_1'][1],
                    self.regions['Muzzle_1'][2] - self.regions['Muzzle_1'][0])
        roi2_size = (self.regions['Muzzle_2'][3] - self.regions['Muzzle_2'][1],
                    self.regions['Muzzle_2'][2] - self.regions['Muzzle_2'][0])
        
        test_roi1 = self._create_structured_test_roi(roi1_size)
        test_roi2 = self._create_structured_test_roi(roi2_size)
        
        # Save test ROIs for inspection
        cv2.imwrite("test_roi1.png", test_roi1)
        cv2.imwrite("test_roi2.png", test_roi2)
        
        algorithms = [
            ("SIFT", self._test_sift_algorithm),
            ("Template_Matching", self._test_template_matching),
            ("ORB", self._test_orb_algorithm)
        ]
        
        results = {}
        
        for algo_name, algo_func in algorithms:
            print(f"\nTesting {algo_name}:")
            try:
                score1, score2 = algo_func(test_roi1, test_roi2)
                results[algo_name] = {
                    'slot1': score1,
                    'slot2': score2,
                    'difference': abs(score1 - score2)
                }
                print(f"  Slot 1: {score1:.4f}")
                print(f"  Slot 2: {score2:.4f}")
                print(f"  Difference: {abs(score1 - score2):.4f}")
            except Exception as e:
                print(f"  Error: {e}")
                results[algo_name] = {'error': str(e)}
        
        return results
    
    def _create_structured_test_roi(self, size):
        """Create a structured test ROI"""
        roi = np.zeros(size, dtype=np.uint8)
        # Add some geometric patterns
        cv2.rectangle(roi, (5, 5), (size[1]-5, size[0]-5), 128, 2)
        cv2.circle(roi, (size[1]//2, size[0]//2), min(size)//6, 255, -1)
        return roi
    
    def _test_sift_algorithm(self, roi1, roi2):
        """Test SIFT algorithm"""
        # Use first template for testing
        template = list(self.muzzle_templates.values())[0]['image']
        template_resized = cv2.resize(template, (roi1.shape[1], roi1.shape[0]))
        
        score1 = match_sift(roi1, template_resized, "Muzzle")
        score2 = match_sift(roi2, template_resized, "Muzzle")
        
        return score1, score2
    
    def _test_template_matching(self, roi1, roi2):
        """Test template matching"""
        template = list(self.muzzle_templates.values())[0]['image']
        template_resized = cv2.resize(template, (roi1.shape[1], roi1.shape[0]))
        
        score1 = template_match_fallback(roi1, template_resized)
        score2 = template_match_fallback(roi2, template_resized)
        
        return score1, score2
    
    def _test_orb_algorithm(self, roi1, roi2):
        """Test ORB algorithm"""
        orb = cv2.ORB_create()
        
        template = list(self.muzzle_templates.values())[0]['image']
        template_resized = cv2.resize(template, (roi1.shape[1], roi1.shape[0]))
        
        # Find keypoints and descriptors
        kp1, des1 = orb.detectAndCompute(roi1, None)
        kp2, des2 = orb.detectAndCompute(roi2, None)
        kp_t, des_t = orb.detectAndCompute(template_resized, None)
        
        if des1 is not None and des_t is not None:
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches1 = bf.match(des1, des_t)
            score1 = len(matches1) / max(len(kp1), 1)
        else:
            score1 = 0.0
        
        if des2 is not None and des_t is not None:
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches2 = bf.match(des2, des_t)
            score2 = len(matches2) / max(len(kp2), 1)
        else:
            score2 = 0.0
        
        return score1, score2

def main():
    """Main comparison function"""
    print("SLOT PROCESSING COMPARISON ANALYSIS")
    print("=" * 60)
    
    comparator = SlotProcessingComparator()
    
    # Run all comparisons
    roi_props = comparator.analyze_roi_characteristics()
    scaling_results = comparator.analyze_template_scaling_impact()
    preprocessing_stats = comparator.analyze_preprocessing_pipeline()
    algorithm_results = comparator.test_detection_algorithms()
    
    print("\n" + "=" * 60)
    print("COMPARISON SUMMARY")
    print("=" * 60)
    
    print("Key findings:")
    print("1. Template size mismatch is the primary issue")
    print("2. ROI sizes are consistent between slots")
    print("3. Preprocessing pipeline shows no significant differences")
    print("4. Algorithm performance varies based on template scaling")

if __name__ == "__main__":
    main()
