import cv2
import mss
import os
import time
import asyncio
from resolution_setting import RESOLUTION_SETTINGS, GUNS_REOLUTION_SETTINGS, Click
import numpy as np
from PIL import ImageGrab

def MSS_Img(Values):
    x1, y1, x2, y2 = Values
    with mss.mss() as sct:
        monitor = {"top": x1, "left": y1, "width": x2, "height": y2}
        img = sct.grab(monitor)
        img_np = np.array(img)
        img_gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)
    return img_gray

def compute_matches_mask(matches, distance_threshold):
    matchesMask = np.zeros((len(matches), 2), dtype=np.int32)
    matchedPoints1 = 0

    for i in range(len(matches)):
        m, n = matches[i]
        if m.distance < distance_threshold * n.distance:
            matchesMask[i, 0] = 1
            matchedPoints1 += 1

    return matchesMask, matchedPoints1

def match_sift(img1, img2):
    sift = cv2.SIFT_create()
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    if des1 is not None and len(des1) > 2 and des2 is not None and len(des2) > 2:
        FLANN_INDEX_KDTREE = 0
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        flann = cv2.FlannBasedMatcher(index_params, search_params)

        matches = flann.knnMatch(des1, des2, k=2)

        matches_array = np.array([[m, n] for m, n in matches], dtype=object)

        matchesMask, matchedPoints1 = compute_matches_mask(matches_array, 0.7)

        totalPoints = len(kp1)
        matchRate = matchedPoints1 / (totalPoints + 0.1)

        return matchRate

    return 0

async def capture_and_compare(Data, imgs):
    Keys = list(Data.keys())[0]
    Values = list(Data.values())[0]
    x1, y1, x2, y2 = Values
    roi = imgs[y1:y2, x1:x2]
    return Keys, roi

async def capture_all_guns(pathData):
    ReturnData = {}
    slot_num = "1" if any(key.endswith('_1') for key in pathData.keys()) else "2"
    print(f"Processing weapon slot {slot_num} with regions: {list(pathData.keys())}")

    for mode, img1 in pathData.items():
        # Extract the component type (Name, Scope, Grip, etc.) from the key
        component_type = mode[:-2]  # Remove the _1 or _2 suffix
        match_Path = f"_internal/data/firearms/{component_type}/"

        if not os.path.exists(match_Path):
            print(f"Warning: Path does not exist: {match_Path}")
            ReturnData[component_type] = "None"
            continue

        content = os.listdir(match_Path)
        MatchValue = 0.0
        MatchName = ""

        print(f"Slot {slot_num} - Processing {component_type}: {len(content)} templates to match")

        for each in content:
            demo_dir = match_Path + each
            img2 = cv2.imread(demo_dir, cv2.IMREAD_GRAYSCALE)
            if img2 is None:
                print(f"Warning: Could not load template image: {demo_dir}")
                continue

            part = match_sift(img1, img2)
            if part > MatchValue:
                MatchName = each[:-4]
                MatchValue = part

        if MatchValue <= 0.0 and not MatchName:
            MatchName = "None"

        print(f"Slot {slot_num} - {component_type}: '{MatchName}' (confidence: {MatchValue:.3f})")
        ReturnData[component_type] = MatchName

    print(f"Slot {slot_num} final results: {ReturnData}")
    return ReturnData

async def capture_all_positions_thread(current_res):
    start_time = time.time()

    Guns_imgs = GUNS_REOLUTION_SETTINGS[current_res]
    Guns_img = RESOLUTION_SETTINGS[current_res]

    # Capture the full image
    Images = MSS_Img(Guns_imgs)

    # Create tasks to process the image
    tasks = [capture_and_compare({k: v}, Images) for k, v in Guns_img.items()]
    captured_images = await asyncio.gather(*tasks)

    # Group captured image data properly by slot number
    Guns1 = {}
    Guns2 = {}

    for key, img in captured_images:
        if key.endswith('_1'):
            Guns1[key] = img
        elif key.endswith('_2'):
            Guns2[key] = img
        else:
            print(f"Warning: Unexpected key format: {key}")

    print(f"Slot 1 regions captured: {list(Guns1.keys())}")
    print(f"Slot 2 regions captured: {list(Guns2.keys())}")

    # Process these images further
    ReturnData = await asyncio.gather(
        capture_all_guns(Guns1),
        capture_all_guns(Guns2)
    )

    elapsed_time = time.time() - start_time
    print(f"Time from capture to recognition: {elapsed_time:.2f} seconds")

    return ReturnData

def recogniseif_firearm(current_res):
    x1, x2 = Click.get(current_res, None)
    screenshot = ImageGrab.grab(bbox=(x1, x2, x1 + 1, x2 + 1))
    r, g, b = screenshot.getpixel((0, 0))
    S_Max, S_Min = 255, 200
    if S_Min <= g <= S_Max and S_Min <= r <= S_Max and S_Min <= b <= S_Max:
        return True
    else:
        return False

