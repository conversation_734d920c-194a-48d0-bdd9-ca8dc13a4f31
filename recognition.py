import cv2
import mss
import os
import time
import asyncio
from resolution_setting import RESOLUTION_SETTINGS, GUNS_REOLUTION_SETTINGS, Click
import numpy as np
from PIL import ImageGrab

def MSS_Img(Values):
    x1, y1, x2, y2 = Values
    with mss.mss() as sct:
        monitor = {"top": x1, "left": y1, "width": x2, "height": y2}
        img = sct.grab(monitor)
        img_np = np.array(img)
        img_gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)
    return img_gray

def compute_matches_mask(matches, distance_threshold):
    matchesMask = np.zeros((len(matches), 2), dtype=np.int32)
    matchedPoints1 = 0

    for i in range(len(matches)):
        m, n = matches[i]
        if m.distance < distance_threshold * n.distance:
            matchesMask[i, 0] = 1
            matchedPoints1 += 1

    return matchesMask, matchedPoints1

def match_sift(img1, img2, component_type="unknown"):
    """Enhanced SIFT matching with component-specific optimizations and accuracy equalization"""
    # For muzzle detection, use multi-scale approach for better accuracy
    if component_type.lower() == "muzzle":
        return enhanced_muzzle_matching(img1, img2)

    # Standard SIFT matching for other components
    sift = cv2.SIFT_create()
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    if des1 is not None and len(des1) > 2 and des2 is not None and len(des2) > 2:
        FLANN_INDEX_KDTREE = 0
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        flann = cv2.FlannBasedMatcher(index_params, search_params)

        matches = flann.knnMatch(des1, des2, k=2)

        matches_array = np.array([[m, n] for m, n in matches], dtype=object)
        matchesMask, matchedPoints1 = compute_matches_mask(matches_array, 0.7)

        totalPoints = len(kp1)
        matchRate = matchedPoints1 / (totalPoints + 0.1)

        return matchRate

    return 0

def enhanced_muzzle_matching(roi_img, template_img):
    """Enhanced muzzle matching with multiple algorithms and accuracy equalization"""
    # Normalize both images to ensure consistent processing
    roi_normalized = normalize_image(roi_img)
    template_normalized = normalize_image(template_img)

    # Get optimal template size for the ROI
    optimal_template = get_optimal_template_size(template_normalized, roi_normalized.shape)

    # Use multiple matching algorithms and combine results
    scores = []

    # 1. Template matching with normalized cross-correlation
    try:
        score1 = template_match_fallback(roi_normalized, optimal_template)
        scores.append(score1)
    except:
        scores.append(0.0)

    # 2. SIFT matching with normalized images
    try:
        score2 = sift_matching_normalized(roi_normalized, optimal_template)
        scores.append(score2)
    except:
        scores.append(0.0)

    # 3. Feature-based matching with ORB (more robust to scaling)
    try:
        score3 = orb_matching(roi_normalized, optimal_template)
        scores.append(score3)
    except:
        scores.append(0.0)

    # Combine scores with weighted average (template matching gets higher weight)
    if len(scores) > 0:
        weights = [0.5, 0.3, 0.2]  # Prioritize template matching
        weighted_score = sum(s * w for s, w in zip(scores, weights[:len(scores)]))
        return max(0.0, weighted_score)  # Ensure non-negative

    return 0.0

def normalize_image(img):
    """Normalize image for consistent processing"""
    if img is None or img.size == 0:
        return img

    # Convert to float for processing
    img_float = img.astype(np.float32)

    # Normalize to 0-1 range
    img_min = img_float.min()
    img_max = img_float.max()

    if img_max > img_min:
        normalized = (img_float - img_min) / (img_max - img_min)
    else:
        normalized = img_float / 255.0

    # Convert back to uint8
    return (normalized * 255).astype(np.uint8)

def get_optimal_template_size(template, roi_shape):
    """Get optimally sized template for the ROI"""
    roi_height, roi_width = roi_shape
    template_height, template_width = template.shape

    # If template is larger than ROI, scale it down
    if template_width > roi_width or template_height > roi_height:
        # Calculate scale factor to fit within ROI while maintaining aspect ratio
        scale_w = roi_width / template_width
        scale_h = roi_height / template_height
        scale = min(scale_w, scale_h) * 0.8  # Use 80% of available space

        new_width = int(template_width * scale)
        new_height = int(template_height * scale)

        # Ensure minimum size
        new_width = max(new_width, 10)
        new_height = max(new_height, 10)

        return cv2.resize(template, (new_width, new_height))

    return template

def sift_matching_normalized(img1, img2):
    """SIFT matching with normalized images"""
    sift = cv2.SIFT_create()
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    if des1 is not None and len(des1) > 2 and des2 is not None and len(des2) > 2:
        FLANN_INDEX_KDTREE = 0
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        flann = cv2.FlannBasedMatcher(index_params, search_params)

        matches = flann.knnMatch(des1, des2, k=2)
        matches_array = np.array([[m, n] for m, n in matches], dtype=object)

        # Use more lenient threshold for muzzle detection
        matchesMask, matchedPoints1 = compute_matches_mask(matches_array, 0.85)

        totalPoints = len(kp1)
        matchRate = matchedPoints1 / (totalPoints + 0.1)

        return matchRate

    return 0.0

def orb_matching(img1, img2):
    """ORB-based matching for robust feature detection"""
    orb = cv2.ORB_create(nfeatures=500)

    kp1, des1 = orb.detectAndCompute(img1, None)
    kp2, des2 = orb.detectAndCompute(img2, None)

    if des1 is not None and des2 is not None and len(des1) > 5 and len(des2) > 5:
        bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
        matches = bf.match(des1, des2)

        # Sort matches by distance
        matches = sorted(matches, key=lambda x: x.distance)

        # Calculate match quality
        good_matches = [m for m in matches if m.distance < 50]  # Threshold for good matches
        match_ratio = len(good_matches) / max(len(kp1), len(kp2), 1)

        return min(match_ratio, 1.0)  # Cap at 1.0

    return 0.0

def template_match_fallback(img1, img2):
    """Enhanced template matching with multiple methods"""
    try:
        h1, w1 = img1.shape
        h2, w2 = img2.shape

        # If template is larger than image, resize template to fit
        if h2 > h1 or w2 > w1:
            scale = min(h1/h2, w1/w2) * 0.9  # Use 90% of available space
            new_h = int(h2 * scale)
            new_w = int(w2 * scale)
            img2_resized = cv2.resize(img2, (new_w, new_h))
        else:
            img2_resized = img2

        # Try multiple template matching methods and take the best result
        methods = [
            cv2.TM_CCOEFF_NORMED,
            cv2.TM_CCORR_NORMED,
            cv2.TM_SQDIFF_NORMED
        ]

        scores = []
        for method in methods:
            try:
                if img2_resized.shape[0] <= img1.shape[0] and img2_resized.shape[1] <= img1.shape[1]:
                    result = cv2.matchTemplate(img1, img2_resized, method)
                    _, max_val, _, _ = cv2.minMaxLoc(result)

                    # For TM_SQDIFF_NORMED, lower values are better, so invert
                    if method == cv2.TM_SQDIFF_NORMED:
                        max_val = 1.0 - max_val

                    scores.append(max_val)
                else:
                    scores.append(0.0)
            except:
                scores.append(0.0)

        # Return the best score
        return max(scores) if scores else 0.0

    except Exception as e:
        print(f"Template matching fallback failed: {e}")
        return 0

async def capture_and_compare(Data, imgs):
    Keys = list(Data.keys())[0]
    Values = list(Data.values())[0]
    x1, y1, x2, y2 = Values
    roi = imgs[y1:y2, x1:x2]
    return Keys, roi

async def capture_all_guns(pathData):
    ReturnData = {}
    slot_num = "1" if any(key.endswith('_1') for key in pathData.keys()) else "2"
    print(f"Processing weapon slot {slot_num} with regions: {list(pathData.keys())}")

    # Store muzzle detection metrics for comparison
    muzzle_metrics = {}

    for mode, img1 in pathData.items():
        # Extract the component type (Name, Scope, Grip, etc.) from the key
        component_type = mode[:-2]  # Remove the _1 or _2 suffix
        match_Path = f"_internal/data/firearms/{component_type}/"

        if not os.path.exists(match_Path):
            print(f"Warning: Path does not exist: {match_Path}")
            ReturnData[component_type] = "None"
            continue

        content = os.listdir(match_Path)
        MatchValue = 0.0
        MatchName = ""

        print(f"Slot {slot_num} - Processing {component_type}: {len(content)} templates to match")

        # Special handling for muzzle detection with enhanced debugging
        if component_type == "Muzzle":
            print(f"Slot {slot_num} - MUZZLE DEBUG: ROI shape: {img1.shape}")
            print(f"Slot {slot_num} - MUZZLE DEBUG: ROI stats - min: {img1.min()}, max: {img1.max()}, mean: {img1.mean():.2f}")

            # Save ROI for debugging if needed
            debug_filename = f"debug_muzzle_slot{slot_num}_roi.png"
            cv2.imwrite(debug_filename, img1)
            print(f"Slot {slot_num} - MUZZLE DEBUG: Saved ROI to {debug_filename}")

            # Initialize muzzle metrics for this slot
            muzzle_metrics[slot_num] = {
                'roi_shape': img1.shape,
                'roi_stats': {
                    'min': float(img1.min()),
                    'max': float(img1.max()),
                    'mean': float(img1.mean()),
                    'std': float(img1.std())
                },
                'template_scores': {},
                'best_match': '',
                'best_score': 0.0
            }

        template_scores = []
        for each in content:
            demo_dir = match_Path + each
            img2 = cv2.imread(demo_dir, cv2.IMREAD_GRAYSCALE)
            if img2 is None:
                print(f"Warning: Could not load template image: {demo_dir}")
                continue

            part = match_sift(img1, img2, component_type)
            template_scores.append(part)

            # Store muzzle template scores for comparison
            if component_type == "Muzzle":
                template_name = each[:-4]
                muzzle_metrics[slot_num]['template_scores'][template_name] = part
                print(f"Slot {slot_num} - MUZZLE DEBUG: {template_name} score: {part:.4f}")

            if part > MatchValue:
                MatchName = each[:-4]
                MatchValue = part

        if MatchValue <= 0.0 and not MatchName:
            MatchName = "None"

        # Update muzzle metrics
        if component_type == "Muzzle":
            muzzle_metrics[slot_num]['best_match'] = MatchName
            muzzle_metrics[slot_num]['best_score'] = MatchValue
            muzzle_metrics[slot_num]['avg_score'] = np.mean(template_scores) if template_scores else 0.0
            muzzle_metrics[slot_num]['score_std'] = np.std(template_scores) if template_scores else 0.0

            print(f"Slot {slot_num} - MUZZLE METRICS:")
            print(f"  Best match: {MatchName} (score: {MatchValue:.4f})")
            print(f"  Average score: {muzzle_metrics[slot_num]['avg_score']:.4f}")
            print(f"  Score std dev: {muzzle_metrics[slot_num]['score_std']:.4f}")

            if MatchValue < 0.1:  # Low confidence threshold
                print(f"Slot {slot_num} - MUZZLE WARNING: Very low confidence score!")
        else:
            print(f"Slot {slot_num} - {component_type}: '{MatchName}' (confidence: {MatchValue:.3f})")

        ReturnData[component_type] = MatchName

    # Store metrics for later comparison
    if 'Muzzle' in [mode[:-2] for mode in pathData.keys()]:
        store_muzzle_metrics(slot_num, muzzle_metrics.get(slot_num, {}))

    print(f"Slot {slot_num} final results: {ReturnData}")
    return ReturnData

# Global storage for muzzle metrics comparison
_muzzle_metrics_storage = {}

def store_muzzle_metrics(slot_num, metrics):
    """Store muzzle metrics for comparison between slots"""
    global _muzzle_metrics_storage
    _muzzle_metrics_storage[slot_num] = metrics

def compare_muzzle_metrics():
    """Compare muzzle detection metrics between slots"""
    global _muzzle_metrics_storage

    if '1' in _muzzle_metrics_storage and '2' in _muzzle_metrics_storage:
        metrics1 = _muzzle_metrics_storage['1']
        metrics2 = _muzzle_metrics_storage['2']

        print("\n" + "="*60)
        print("MUZZLE DETECTION ACCURACY COMPARISON")
        print("="*60)

        print(f"Slot 1 - Best: {metrics1.get('best_match', 'None')} ({metrics1.get('best_score', 0):.4f})")
        print(f"Slot 2 - Best: {metrics2.get('best_match', 'None')} ({metrics2.get('best_score', 0):.4f})")

        score_diff = abs(metrics1.get('best_score', 0) - metrics2.get('best_score', 0))
        avg_diff = abs(metrics1.get('avg_score', 0) - metrics2.get('avg_score', 0))

        print(f"Score difference: {score_diff:.4f}")
        print(f"Average score difference: {avg_diff:.4f}")

        if score_diff > 0.05:  # 5% threshold
            print("⚠️  SIGNIFICANT ACCURACY DIFFERENCE DETECTED!")
        else:
            print("✓ Accuracy difference within acceptable range")

        # Compare ROI statistics
        roi1_stats = metrics1.get('roi_stats', {})
        roi2_stats = metrics2.get('roi_stats', {})

        print("\nROI Statistics Comparison:")
        for stat in ['mean', 'std', 'min', 'max']:
            val1 = roi1_stats.get(stat, 0)
            val2 = roi2_stats.get(stat, 0)
            diff = abs(val1 - val2)
            print(f"  {stat:4}: Slot1={val1:7.2f}, Slot2={val2:7.2f}, Diff={diff:7.2f}")

        print("="*60)

async def capture_all_positions_thread(current_res):
    start_time = time.time()

    Guns_imgs = GUNS_REOLUTION_SETTINGS[current_res]
    Guns_img = RESOLUTION_SETTINGS[current_res]

    # Capture the full image
    Images = MSS_Img(Guns_imgs)

    # Create tasks to process the image
    tasks = [capture_and_compare({k: v}, Images) for k, v in Guns_img.items()]
    captured_images = await asyncio.gather(*tasks)

    # Group captured image data properly by slot number
    Guns1 = {}
    Guns2 = {}

    for key, img in captured_images:
        if key.endswith('_1'):
            Guns1[key] = img
        elif key.endswith('_2'):
            Guns2[key] = img
        else:
            print(f"Warning: Unexpected key format: {key}")

    print(f"Slot 1 regions captured: {list(Guns1.keys())}")
    print(f"Slot 2 regions captured: {list(Guns2.keys())}")

    # Process these images further
    ReturnData = await asyncio.gather(
        capture_all_guns(Guns1),
        capture_all_guns(Guns2)
    )

    # Compare muzzle detection accuracy between slots
    compare_muzzle_metrics()

    elapsed_time = time.time() - start_time
    print(f"Time from capture to recognition: {elapsed_time:.2f} seconds")

    return ReturnData

def recogniseif_firearm(current_res):
    x1, x2 = Click.get(current_res, None)
    screenshot = ImageGrab.grab(bbox=(x1, x2, x1 + 1, x2 + 1))
    r, g, b = screenshot.getpixel((0, 0))
    S_Max, S_Min = 255, 200
    if S_Min <= g <= S_Max and S_Min <= r <= S_Max and S_Min <= b <= S_Max:
        return True
    else:
        return False

