from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QGroupBox, QTreeWidget,
                           QTreeWidgetItem, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import json
import os

class WeaponEditor(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Редактор паттернов отдачи")
        self.setMinimumSize(600, 800)
        self.patterns = {}
        self.setup_ui()
        self.load_patterns()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        tree_group = QGroupBox("Список оружия")
        tree_layout = QVBoxLayout(tree_group)
        
        self.weapon_tree = QTreeWidget()
        self.weapon_tree.setHeader<PERSON><PERSON>ls(["Оружие", "Значение отдачи"])
        self.weapon_tree.setFont(QFont('Segoe UI', 10))
        self.weapon_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #242424;
                color: #b388ff;
                border: 1px solid #4a148c;
                border-radius: 4px;
            }
            QTreeWidget::item:selected {
                background-color: #4a148c;
            }
            QTreeWidget::item:hover {
                background-color: #6a1b9a;
            }
        """)
        tree_layout.addWidget(self.weapon_tree)

        edit_group = QGroupBox("Редактирование")
        edit_layout = QVBoxLayout(edit_group)

        controls_layout = QHBoxLayout()

        weapon_layout = QVBoxLayout()
        weapon_label = QLabel("Оружие:")
        self.weapon_combo = QComboBox()
        weapon_layout.addWidget(weapon_label)
        weapon_layout.addWidget(self.weapon_combo)

        value_layout = QVBoxLayout()
        value_label = QLabel("Значение отдачи:")
        self.value_spin = QDoubleSpinBox()
        self.value_spin.setRange(0, 100)
        self.value_spin.setDecimals(1) 
        self.value_spin.setSingleStep(0.1)  
        value_layout.addWidget(value_label)
        value_layout.addWidget(self.value_spin)
        
        controls_layout.addLayout(weapon_layout)
        controls_layout.addLayout(value_layout)
        edit_layout.addLayout(controls_layout)

        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("Добавить/Обновить")
        self.add_btn.clicked.connect(self.add_or_update_pattern)
        self.remove_btn = QPushButton("Удалить")
        self.remove_btn.clicked.connect(self.remove_pattern)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.remove_btn)
        edit_layout.addLayout(button_layout)
 
        layout.addWidget(tree_group, stretch=2)
        layout.addWidget(edit_group, stretch=1)

        dialog_buttons = QHBoxLayout()
        save_btn = QPushButton("Сохранить все")
        save_btn.clicked.connect(self.save_patterns)
        cancel_btn = QPushButton("Отмена")
        cancel_btn.clicked.connect(self.reject)
        
        dialog_buttons.addWidget(save_btn)
        dialog_buttons.addWidget(cancel_btn)
        layout.addLayout(dialog_buttons)

        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
            }
            QGroupBox {
                background-color: #242424;
                border: 1px solid #4a148c;
                border-radius: 8px;
                color: #e1bee7;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
            QLabel {
                color: #e1bee7;
                font-size: 11px;
            }
            QPushButton {
                background-color: #4a148c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6a1b9a;
            }
            QComboBox, QSpinBox {
                background-color: #242424;
                color: #b388ff;
                border: 1px solid #4a148c;
                border-radius: 4px;
                padding: 5px;
            }
        """)

    def load_patterns(self):
        """Загрузка паттернов из файла"""
        try:
            pattern_file = './Config/weapon_patterns.json'
            if os.path.exists(pattern_file):
                with open(pattern_file, 'r', encoding='utf-8') as f:
                    self.patterns = json.load(f)
            else:
                import macro
                self.patterns = macro.weapon_patterns.copy()

            self.update_weapon_list()
            self.weapon_combo.addItems(sorted(self.patterns.keys()))
            
        except Exception as e:
            QMessageBox.warning(self, "Ошибка", f"Не удалось загрузить паттерны: {str(e)}")

    def update_weapon_list(self):
        """Обновление списка оружия в дереве"""
        self.weapon_tree.clear()
        for weapon, recoil in sorted(self.patterns.items()):

            item = QTreeWidgetItem([weapon, f"{float(recoil):.1f}"])
            self.weapon_tree.addTopLevelItem(item)

    def add_or_update_pattern(self):
        """Добавление или обновление паттерна отдачи"""
        weapon = self.weapon_combo.currentText()
        if not weapon:
            QMessageBox.warning(self, "Ошибка", "Выберите оружие")
            return
            
        recoil = self.value_spin.value()
        self.patterns[weapon] = float(recoil)
        
        self.update_weapon_list()
        QMessageBox.information(self, "Успех", f"Паттерн для {weapon} обновлен: {recoil:.1f}")

    def remove_pattern(self):
        """Удаление паттерна отдачи"""
        weapon = self.weapon_combo.currentText()
        if not weapon:
            QMessageBox.warning(self, "Ошибка", "Выберите оружие")
            return
            
        if weapon in self.patterns:
            del self.patterns[weapon]
            self.update_weapon_list()
            QMessageBox.information(self, "Успех", f"Паттерн для {weapon} удален")

    def save_patterns(self):
        """Сохранение всех паттернов в файл"""
        try:
            with open('./Config/weapon_patterns.json', 'w', encoding='utf-8') as f:
                json.dump(self.patterns, f, indent=4, ensure_ascii=False)
            QMessageBox.information(self, "Успех", "Паттерны сохранены")
            self.accept()
        except Exception as e:
            QMessageBox.warning(self, "Ошибка", f"Не удалось сохранить паттерны: {str(e)}")

    def reject(self):
        """Отмена изменений"""
        if QMessageBox.question(self, "Подтверждение", 
                              "Отменить все изменения?",
                              QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes:
            super().reject()
