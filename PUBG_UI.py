from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets
import os
import macro

class Ui_PUBG(object):
    def setupUi(self, PUBG):
        PUBG.setObjectName("PUBG")
        PUBG.setWindowModality(QtCore.Qt.ApplicationModal)
        PUBG.resize(500, 600)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("./_internal/GHUB.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        PUBG.setWindowIcon(icon)
        PUBG.setWindowFilePath("")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(PUBG)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.Container = QtWidgets.QWidget(PUBG)
        self.Container.setObjectName("Container")
        self.gridLayout = QtWidgets.QGridLayout(self.Container)
        self.gridLayout.setContentsMargins(2, 2, 2, 2)
        self.gridLayout.setSpacing(2)
        self.gridLayout.setObjectName("gridLayout")
        self.TitleBox = QtWidgets.QWidget(self.Container)
        self.TitleBox.setObjectName("TitleBox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.TitleBox)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.Title = QtWidgets.QLabel(self.TitleBox)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.Title.setFont(font)
        self.Title.setTabletTracking(False)
        self.Title.setFocusPolicy(QtCore.Qt.NoFocus)
        self.Title.setTextFormat(QtCore.Qt.RichText)
        self.Title.setScaledContents(False)
        self.Title.setAlignment(QtCore.Qt.AlignCenter)
        self.Title.setWordWrap(False)
        self.Title.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse)
        self.Title.setObjectName("Title")
        self.verticalLayout_4.addWidget(self.Title)
        self.WinVersion = QtWidgets.QLabel(self.TitleBox)
        self.WinVersion.setText("")
        self.WinVersion.setAlignment(QtCore.Qt.AlignCenter)
        self.WinVersion.setObjectName("WinVersion")
        self.verticalLayout_4.addWidget(self.WinVersion)
        self.gridLayout.addWidget(self.TitleBox, 0, 0, 1, 1)
        self.FunctionBox = QtWidgets.QWidget(self.Container)
        self.FunctionBox.setObjectName("FunctionBox")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.FunctionBox)
        self.gridLayout_2.setContentsMargins(2, 2, 2, 2)
        self.gridLayout_2.setSpacing(2)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.ScopeBox = QtWidgets.QWidget(self.FunctionBox)
        self.ScopeBox.setObjectName("ScopeBox")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.ScopeBox)
        self.horizontalLayout_15.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_15.setSpacing(0)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.ScopeMode = QtWidgets.QGroupBox(self.ScopeBox)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.ScopeMode.setFont(font)
        self.ScopeMode.setObjectName("ScopeMode")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.ScopeMode)
        self.horizontalLayout_16.setContentsMargins(5, 0, 5, 0)
        self.horizontalLayout_16.setSpacing(6)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.LongPress = QtWidgets.QRadioButton(self.ScopeMode)
        self.LongPress.setObjectName("LongPress")
        self.horizontalLayout_16.addWidget(self.LongPress)
        self.ClickPress = QtWidgets.QRadioButton(self.ScopeMode)
        self.ClickPress.setObjectName("ClickPress")
        self.horizontalLayout_16.addWidget(self.ClickPress)
        self.horizontalLayout_16.setStretch(0, 3)
        self.horizontalLayout_16.setStretch(1, 2)
        self.horizontalLayout_15.addWidget(self.ScopeMode)
        self.IsScope = QtWidgets.QGroupBox(self.ScopeBox)
        self.IsScope.setEnabled(False)
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setUnderline(False)
        font.setWeight(75)
        font.setStrikeOut(False)
        font.setKerning(False)
        font.setStyleStrategy(QtGui.QFont.PreferDefault)
        self.IsScope.setFont(font)
        self.IsScope.setObjectName("IsScope")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.IsScope)
        self.horizontalLayout_17.setContentsMargins(5, 0, 5, 0)
        self.horizontalLayout_17.setSpacing(6)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.OpenScope = QtWidgets.QRadioButton(self.IsScope)
        self.OpenScope.setObjectName("OpenScope")
        self.horizontalLayout_17.addWidget(self.OpenScope)
        self.CloseScope = QtWidgets.QRadioButton(self.IsScope)
        self.CloseScope.setObjectName("CloseScope")
        self.horizontalLayout_17.addWidget(self.CloseScope)
        self.horizontalLayout_15.addWidget(self.IsScope)
        self.horizontalLayout_15.setStretch(0, 1)
        self.horizontalLayout_15.setStretch(1, 1)
        self.gridLayout_2.addWidget(self.ScopeBox, 2, 0, 1, 1)
        self.IdentifyBox = QtWidgets.QWidget(self.FunctionBox)
        self.IdentifyBox.setObjectName("IdentifyBox")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.IdentifyBox)
        self.horizontalLayout_4.setContentsMargins(2, 2, 2, 2)
        self.horizontalLayout_4.setSpacing(2)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.OneGunsBox = QtWidgets.QGroupBox(self.IdentifyBox)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.OneGunsBox.setFont(font)
        self.OneGunsBox.setAlignment(QtCore.Qt.AlignCenter)
        self.OneGunsBox.setFlat(True)
        self.OneGunsBox.setObjectName("OneGunsBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.OneGunsBox)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.Name1Box = QtWidgets.QWidget(self.OneGunsBox)
        self.Name1Box.setObjectName("Name1Box")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.Name1Box)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.Name1Lable = QtWidgets.QLabel(self.Name1Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Name1Lable.setFont(font)
        self.Name1Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Name1Lable.setObjectName("Name1Lable")
        self.horizontalLayout_5.addWidget(self.Name1Lable)
        self.Name1Name = QtWidgets.QLabel(self.Name1Box)
        self.Name1Name.setStyleSheet("color: rgb(56, 56, 56);\n"
"font: 12pt \"Arial Rounded MT Bold\";")
        self.Name1Name.setText("")
        self.Name1Name.setObjectName("Name1Name")
        self.horizontalLayout_5.addWidget(self.Name1Name)
        self.verticalLayout.addWidget(self.Name1Box)
        self.Muzzle1Box = QtWidgets.QWidget(self.OneGunsBox)
        self.Muzzle1Box.setObjectName("Muzzle1Box")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.Muzzle1Box)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.Muzzle1Lable = QtWidgets.QLabel(self.Muzzle1Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Muzzle1Lable.setFont(font)
        self.Muzzle1Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Muzzle1Lable.setObjectName("Muzzle1Lable")
        self.horizontalLayout_6.addWidget(self.Muzzle1Lable)
        self.Muzzle1Name = QtWidgets.QLabel(self.Muzzle1Box)
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(9)
        self.Muzzle1Name.setFont(font)
        self.Muzzle1Name.setStyleSheet("color: rgb(0, 134, 0);\n"
"font: 75 10pt \"Arial\";")
        self.Muzzle1Name.setText("")
        self.Muzzle1Name.setObjectName("Muzzle1Name")
        self.horizontalLayout_6.addWidget(self.Muzzle1Name)
        self.verticalLayout.addWidget(self.Muzzle1Box)
        self.Scope1Box = QtWidgets.QWidget(self.OneGunsBox)
        self.Scope1Box.setObjectName("Scope1Box")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.Scope1Box)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.Scope1Lable = QtWidgets.QLabel(self.Scope1Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Scope1Lable.setFont(font)
        self.Scope1Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Scope1Lable.setObjectName("Scope1Lable")
        self.horizontalLayout_8.addWidget(self.Scope1Lable)
        self.Scope1Name = QtWidgets.QLabel(self.Scope1Box)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(10)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(9)
        self.Scope1Name.setFont(font)
        self.Scope1Name.setStyleSheet("color: rgb(255, 8, 45);\n"
"font: 75 10pt \"Agency FB\";")
        self.Scope1Name.setText("")
        self.Scope1Name.setObjectName("Scope1Name")
        self.horizontalLayout_8.addWidget(self.Scope1Name)
        self.verticalLayout.addWidget(self.Scope1Box)
        self.Grip1Box = QtWidgets.QWidget(self.OneGunsBox)
        self.Grip1Box.setObjectName("Grip1Box")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.Grip1Box)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.Grip1Lable = QtWidgets.QLabel(self.Grip1Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Grip1Lable.setFont(font)
        self.Grip1Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Grip1Lable.setObjectName("Grip1Lable")
        self.horizontalLayout_7.addWidget(self.Grip1Lable)
        self.Grip1Name = QtWidgets.QLabel(self.Grip1Box)
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(9)
        self.Grip1Name.setFont(font)
        self.Grip1Name.setStyleSheet("color: rgb(168, 84, 0);\n"
"font: 75 10pt \"Arial\";")
        self.Grip1Name.setText("")
        self.Grip1Name.setObjectName("Grip1Name")
        self.horizontalLayout_7.addWidget(self.Grip1Name)
        self.verticalLayout.addWidget(self.Grip1Box)
        self.Butt1Box = QtWidgets.QWidget(self.OneGunsBox)
        self.Butt1Box.setObjectName("Butt1Box")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.Butt1Box)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.Butt1Label = QtWidgets.QLabel(self.Butt1Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Butt1Label.setFont(font)
        self.Butt1Label.setAlignment(QtCore.Qt.AlignCenter)
        self.Butt1Label.setObjectName("Butt1Label")
        self.horizontalLayout_9.addWidget(self.Butt1Label)
        self.Butt1Name = QtWidgets.QLabel(self.Butt1Box)
        self.Butt1Name.setStyleSheet("color:rgb(0, 85, 255);\n"
"font: 75 10pt \"Arial\";")
        self.Butt1Name.setText("")
        self.Butt1Name.setObjectName("Butt1Name")
        self.horizontalLayout_9.addWidget(self.Butt1Name)
        self.verticalLayout.addWidget(self.Butt1Box)
        self.horizontalLayout_4.addWidget(self.OneGunsBox)
        self.TwoGunsBox = QtWidgets.QGroupBox(self.IdentifyBox)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.TwoGunsBox.setFont(font)
        self.TwoGunsBox.setAlignment(QtCore.Qt.AlignCenter)
        self.TwoGunsBox.setFlat(True)
        self.TwoGunsBox.setObjectName("TwoGunsBox")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.TwoGunsBox)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.Name2Box = QtWidgets.QWidget(self.TwoGunsBox)
        self.Name2Box.setObjectName("Name2Box")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.Name2Box)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.Name2Lable = QtWidgets.QLabel(self.Name2Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Name2Lable.setFont(font)
        self.Name2Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Name2Lable.setObjectName("Name2Lable")
        self.horizontalLayout_10.addWidget(self.Name2Lable)
        self.Name2Name = QtWidgets.QLabel(self.Name2Box)
        self.Name2Name.setStyleSheet("color: rgb(56, 56, 56);\n"
"font: 12pt \"Arial Rounded MT Bold\";")
        self.Name2Name.setText("")
        self.Name2Name.setObjectName("Name2Name")
        self.horizontalLayout_10.addWidget(self.Name2Name)
        self.verticalLayout_2.addWidget(self.Name2Box)
        self.Muzzle2Box = QtWidgets.QWidget(self.TwoGunsBox)
        self.Muzzle2Box.setObjectName("Muzzle2Box")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.Muzzle2Box)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.Muzzle2Lable = QtWidgets.QLabel(self.Muzzle2Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Muzzle2Lable.setFont(font)
        self.Muzzle2Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Muzzle2Lable.setObjectName("Muzzle2Lable")
        self.horizontalLayout_11.addWidget(self.Muzzle2Lable)
        self.Muzzle2Name = QtWidgets.QLabel(self.Muzzle2Box)
        self.Muzzle2Name.setStyleSheet("color: rgb(0, 134, 0);\n"
"font: 75 10pt \"Arial\";")
        self.Muzzle2Name.setText("")
        self.Muzzle2Name.setObjectName("Muzzle2Name")
        self.horizontalLayout_11.addWidget(self.Muzzle2Name)
        self.verticalLayout_2.addWidget(self.Muzzle2Box)
        self.Scope2Box = QtWidgets.QWidget(self.TwoGunsBox)
        self.Scope2Box.setObjectName("Scope2Box")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.Scope2Box)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setSpacing(0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.Scope2Lable = QtWidgets.QLabel(self.Scope2Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Scope2Lable.setFont(font)
        self.Scope2Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Scope2Lable.setObjectName("Scope2Lable")
        self.horizontalLayout_12.addWidget(self.Scope2Lable)
        self.Scope2Name = QtWidgets.QLabel(self.Scope2Box)
        self.Scope2Name.setStyleSheet("color: rgb(255, 8, 45);\n"
"font: 75 10pt \"Agency FB\";")
        self.Scope2Name.setText("")
        self.Scope2Name.setObjectName("Scope2Name")
        self.horizontalLayout_12.addWidget(self.Scope2Name)
        self.verticalLayout_2.addWidget(self.Scope2Box)
        self.Grip2Box = QtWidgets.QWidget(self.TwoGunsBox)
        self.Grip2Box.setObjectName("Grip2Box")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.Grip2Box)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setSpacing(0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.Grip2Lable = QtWidgets.QLabel(self.Grip2Box)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Grip2Lable.setFont(font)
        self.Grip2Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Grip2Lable.setObjectName("Grip2Lable")
        self.horizontalLayout_14.addWidget(self.Grip2Lable)
        self.Grip2Name = QtWidgets.QLabel(self.Grip2Box)
        self.Grip2Name.setStyleSheet("color:rgb(0, 85, 255);\n"
"font: 75 10pt \"Arial\";")
        self.Grip2Name.setText("")
        self.Grip2Name.setObjectName("Grip2Name")
        self.horizontalLayout_14.addWidget(self.Grip2Name)
        self.verticalLayout_2.addWidget(self.Grip2Box)
        self.Butt2Box = QtWidgets.QWidget(self.TwoGunsBox)
        self.Butt2Box.setObjectName("Butt2Box")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.Butt2Box)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.Butt2Lable = QtWidgets.QLabel(self.Butt2Box)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.Butt2Lable.setFont(font)
        self.Butt2Lable.setAlignment(QtCore.Qt.AlignCenter)
        self.Butt2Lable.setObjectName("Butt2Lable")
        self.horizontalLayout_13.addWidget(self.Butt2Lable)
        self.Butt2Name = QtWidgets.QLabel(self.Butt2Box)
        self.Butt2Name.setStyleSheet("color: rgb(168, 84, 0);\n"
"font: 75 10pt \"Arial\";")
        self.Butt2Name.setText("")
        self.Butt2Name.setObjectName("Butt2Name")
        self.horizontalLayout_13.addWidget(self.Butt2Name)
        self.verticalLayout_2.addWidget(self.Butt2Box)
        self.horizontalLayout_4.addWidget(self.TwoGunsBox)
        self.gridLayout_2.addWidget(self.IdentifyBox, 0, 0, 1, 1)
        self.InfoBox = QtWidgets.QWidget(self.FunctionBox)
        self.InfoBox.setObjectName("InfoBox")
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout(self.InfoBox)
        self.horizontalLayout_21.setContentsMargins(2, 2, 2, 2)
        self.horizontalLayout_21.setSpacing(2)
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.InfoBoxs = QtWidgets.QGroupBox(self.InfoBox)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.InfoBoxs.setFont(font)
        self.InfoBoxs.setAlignment(QtCore.Qt.AlignCenter)
        self.InfoBoxs.setObjectName("InfoBoxs")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout(self.InfoBoxs)
        self.horizontalLayout_22.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_22.setSpacing(0)
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")

        self.Info = QtWidgets.QTextEdit(self.InfoBoxs)
        self.Info.setReadOnly(True)
        self.Info.setObjectName("Info")
        self.Info.setMinimumHeight(100)
        self.Info.setMaximumHeight(150)
        self.horizontalLayout_22.addWidget(self.Info)

        self.Info.setStyleSheet("""
            QTextEdit {
                background-color: #242424;
                color: #b388ff;
                border: 1px solid #4a148c;
                border-radius: 4px;
                padding: 5px;
                font-size: 10px;  /* Уменьшаем размер шрифта */
                min-height: 100px;
                max-height: 150px;
            }
        """)
        
        self.horizontalLayout_21.addWidget(self.InfoBoxs)
        self.gridLayout_2.addWidget(self.InfoBox, 3, 0, 1, 1)
        self.ParameterBox = QtWidgets.QWidget(self.FunctionBox)
        self.ParameterBox.setObjectName("ParameterBox")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.ParameterBox)
        self.horizontalLayout_18.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_18.setSpacing(0)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.EquipBox = QtWidgets.QGroupBox(self.ParameterBox)
        self.EquipBox.setEnabled(False)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.EquipBox.setFont(font)
        self.EquipBox.setFlat(False)
        self.EquipBox.setObjectName("EquipBox")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.EquipBox)
        self.horizontalLayout_20.setContentsMargins(5, 0, 5, 0)
        self.horizontalLayout_20.setSpacing(6)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.OneGuns = QtWidgets.QRadioButton(self.EquipBox)
        self.OneGuns.setStyleSheet("color: rgb(255, 0, 0);")
        self.OneGuns.setObjectName("OneGuns")
        self.horizontalLayout_20.addWidget(self.OneGuns)
        self.TwoGuns = QtWidgets.QRadioButton(self.EquipBox)
        self.TwoGuns.setStyleSheet("color: rgb(255, 0, 0);")
        self.TwoGuns.setObjectName("TwoGuns")
        self.horizontalLayout_20.addWidget(self.TwoGuns)
        self.Orders = QtWidgets.QRadioButton(self.EquipBox)
        self.Orders.setObjectName("Orders")
        self.horizontalLayout_20.addWidget(self.Orders)
        self.horizontalLayout_18.addWidget(self.EquipBox)
        self.PoseBox = QtWidgets.QGroupBox(self.ParameterBox)
        self.PoseBox.setEnabled(False)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.PoseBox.setFont(font)
        self.PoseBox.setObjectName("PoseBox")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout(self.PoseBox)
        self.horizontalLayout_19.setContentsMargins(5, 0, 5, 0)
        self.horizontalLayout_19.setSpacing(6)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.SquatDown = QtWidgets.QRadioButton(self.PoseBox)
        self.SquatDown.setObjectName("SquatDown")
        self.horizontalLayout_19.addWidget(self.SquatDown)
        self.GetDown = QtWidgets.QRadioButton(self.PoseBox)
        self.GetDown.setObjectName("GetDown")
        self.horizontalLayout_19.addWidget(self.GetDown)
        self.Stand = QtWidgets.QRadioButton(self.PoseBox)
        self.Stand.setCheckable(True)
        self.Stand.setChecked(False)
        self.Stand.setAutoRepeat(False)
        self.Stand.setObjectName("Stand")
        self.horizontalLayout_19.addWidget(self.Stand)
        self.horizontalLayout_18.addWidget(self.PoseBox)
        self.horizontalLayout_18.setStretch(0, 1)
        self.horizontalLayout_18.setStretch(1, 1)
        self.gridLayout_2.addWidget(self.ParameterBox, 1, 0, 1, 1)
        self.gridLayout_2.setRowStretch(0, 2)
        self.gridLayout_2.setRowStretch(1, 1)
        self.gridLayout_2.setRowStretch(2, 1)
        self.gridLayout_2.setRowStretch(3, 2)
        self.gridLayout.addWidget(self.FunctionBox, 2, 0, 1, 1)
        self.CtrlStatusBox = QtWidgets.QWidget(self.Container)
        self.CtrlStatusBox.setObjectName("CtrlStatusBox")
        self.horizontalLayout_Ctrl = QtWidgets.QHBoxLayout(self.CtrlStatusBox)
        self.horizontalLayout_Ctrl.setContentsMargins(2, 2, 2, 2)
        self.horizontalLayout_Ctrl.setSpacing(0)
        self.horizontalLayout_Ctrl.setObjectName("horizontalLayout_Ctrl")
        self.CtrlStatusLabel = QtWidgets.QLabel(self.CtrlStatusBox)
        self.CtrlStatusLabel.setObjectName("CtrlStatusLabel")
        self.horizontalLayout_Ctrl.addWidget(self.CtrlStatusLabel)
        self.CtrlStatus = QtWidgets.QLabel(self.CtrlStatusBox)
        self.CtrlStatus.setStyleSheet("color: rgb(255, 0, 0);")
        self.CtrlStatus.setObjectName("CtrlStatus")
        self.horizontalLayout_Ctrl.addWidget(self.CtrlStatus)
        self.gridLayout.addWidget(self.CtrlStatusBox, 4, 0, 1, 1)

        self.ShiftStatusBox = QtWidgets.QWidget(self.Container)
        self.ShiftStatusBox.setObjectName("ShiftStatusBox")
        self.horizontalLayout_Shift = QtWidgets.QHBoxLayout(self.ShiftStatusBox)
        self.horizontalLayout_Shift.setContentsMargins(2, 2, 2, 2)
        self.horizontalLayout_Shift.setSpacing(0)
        self.horizontalLayout_Shift.setObjectName("horizontalLayout_Shift")
        self.ShiftStatusLabel = QtWidgets.QLabel(self.ShiftStatusBox)
        self.ShiftStatusLabel.setObjectName("ShiftStatusLabel")
        self.horizontalLayout_Shift.addWidget(self.ShiftStatusLabel)
        self.ShiftStatus = QtWidgets.QLabel(self.ShiftStatusBox)
        self.ShiftStatus.setStyleSheet("color: rgb(255, 0, 0);")
        self.ShiftStatus.setObjectName("ShiftStatus")
        self.horizontalLayout_Shift.addWidget(self.ShiftStatus)
        self.gridLayout.addWidget(self.ShiftStatusBox, 5, 0, 1, 1)
        self.ResolutionBox = QtWidgets.QWidget(self.Container)
        self.ResolutionBox.setObjectName("ResolutionBox")
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout(self.ResolutionBox)
        self.horizontalLayout_23.setContentsMargins(2, 2, 2, 2)
        self.horizontalLayout_23.setSpacing(2)
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.Resolution = QtWidgets.QGroupBox(self.ResolutionBox)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.Resolution.setFont(font)
        self.Resolution.setAlignment(QtCore.Qt.AlignCenter)
        self.Resolution.setFlat(True)
        self.Resolution.setCheckable(False)
        self.Resolution.setObjectName("Resolution")
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout(self.Resolution)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.ResolutionLabel = QtWidgets.QLabel(self.Resolution)
        self.ResolutionLabel.setObjectName("ResolutionLabel")
        self.horizontalLayout_24.addWidget(self.ResolutionLabel)
        self.ResolutionSelect = QtWidgets.QComboBox(self.Resolution)
        self.ResolutionSelect.setObjectName("ResolutionSelect")
        self.ResolutionSelect.addItem("")
        self.ResolutionSelect.setItemText(0, "1920x1080")
        self.ResolutionSelect.addItem("")
        self.ResolutionSelect.addItem("")
        self.ResolutionSelect.addItem("")
        self.ResolutionSelect.addItem("")
        self.ResolutionSelect.addItem("")
        self.ResolutionSelect.addItem("")
        self.horizontalLayout_24.addWidget(self.ResolutionSelect)
        self.ResolutionBtn = QtWidgets.QPushButton(self.Resolution)
        self.ResolutionBtn.setObjectName("ResolutionBtn")
        self.horizontalLayout_24.addWidget(self.ResolutionBtn)
        self.horizontalLayout_24.setStretch(0, 1)
        self.horizontalLayout_24.setStretch(1, 2)
        self.horizontalLayout_24.setStretch(2, 1)
        self.horizontalLayout_23.addWidget(self.Resolution)
        self.horizontalLayout_23.setStretch(0, 1)
        self.gridLayout.addWidget(self.ResolutionBox, 6, 0, 1, 1)

        self.CrouchKeyBox = QtWidgets.QGroupBox(self.Container)
        self.CrouchKeyBox.setObjectName("CrouchKeyBox")
        self.CrouchKeyBox.setTitle("Клавиша приседания")
        self.horizontalLayout_crouch = QtWidgets.QHBoxLayout(self.CrouchKeyBox)
        
        self.CrouchKeyCombo = QtWidgets.QComboBox(self.CrouchKeyBox)
        self.CrouchKeyCombo.addItems(["CTRL", "ALT", "C"])
        self.horizontalLayout_crouch.addWidget(self.CrouchKeyCombo)

        self.CrouchKeyBtn = QtWidgets.QPushButton(self.CrouchKeyBox)
        self.CrouchKeyBtn.setText("Сохранить")
        self.horizontalLayout_crouch.addWidget(self.CrouchKeyBtn)

        self.gridLayout.addWidget(self.CrouchKeyBox, 7, 0, 1, 1)

        self.ListenModeBox = QtWidgets.QGroupBox(self.Container)
        self.ListenModeBox.setObjectName("ListenModeBox")
        self.ListenModeBox.setTitle("Режим прослушивания")
        self.horizontalLayout_listen = QtWidgets.QHBoxLayout(self.ListenModeBox)

        self.ListenBothRadio = QtWidgets.QRadioButton("Оба слота")
        self.ListenOneRadio = QtWidgets.QRadioButton("Только слот 1")
        self.ListenTwoRadio = QtWidgets.QRadioButton("Только слот 2")

        self.horizontalLayout_listen.addWidget(self.ListenBothRadio)
        self.horizontalLayout_listen.addWidget(self.ListenOneRadio)
        self.horizontalLayout_listen.addWidget(self.ListenTwoRadio)

        self.ListenBothRadio.setChecked(True)

        self.EditPatternsBtn = QtWidgets.QPushButton(self.Container)
        self.EditPatternsBtn.setText("Редактировать отдачу")

        self.CrosshairBtn = QtWidgets.QPushButton(self.Container)
        self.CrosshairBtn.setObjectName("CrosshairBtn")
        self.CrosshairBtn.setText("Включить прицел")
        self.CrosshairBtn.setCheckable(True)

        self.gridLayout.addWidget(self.ListenModeBox, 8, 0, 1, 1)
        self.gridLayout.addWidget(self.CrosshairBtn, 9, 0, 1, 1)

        self.CrosshairSettingsBox = QtWidgets.QGroupBox(self.Container)
        self.CrosshairSettingsBox.setObjectName("CrosshairSettingsBox")
        self.CrosshairSettingsBox.setTitle("Настройки прицела")
        self.horizontalLayout_crosshair = QtWidgets.QHBoxLayout(self.CrosshairSettingsBox)

        self.CrosshairTypeCombo = QtWidgets.QComboBox(self.CrosshairSettingsBox)
        self.CrosshairTypeCombo.addItems(["Простой", "Свастика"])
        self.horizontalLayout_crosshair.addWidget(self.CrosshairTypeCombo)

        self.CrosshairColorCombo = QtWidgets.QComboBox(self.CrosshairSettingsBox)
        self.CrosshairColorCombo.addItems(["Белый", "Красный", "Зеленый", "Синий"])
        self.horizontalLayout_crosshair.addWidget(self.CrosshairColorCombo)

        self.gridLayout.addWidget(self.CrosshairSettingsBox, 10, 0, 1, 1)

        self.CrouchToggleCheck = QtWidgets.QCheckBox("Режим переключения", self.Container)
        self.OverlayBtn = QtWidgets.QPushButton("Включить оверлей", self.Container)

        self.gridLayout.addWidget(self.CrouchToggleCheck, 11, 0, 1, 1)
        self.gridLayout.addWidget(self.OverlayBtn, 12, 0, 1, 1)

        self.BtnBox = QtWidgets.QWidget(self.Container)
        self.BtnBox.setObjectName("BtnBox")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.BtnBox)
        self.horizontalLayout_3.setContentsMargins(2, 2, 2, 2)
        self.horizontalLayout_3.setSpacing(4)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")

        self.StatusInfo = QtWidgets.QLineEdit(self.BtnBox)
        self.StatusInfo.setStyleSheet("color: rgb(170, 0, 0);")
        self.StatusInfo.setDragEnabled(False)
        self.StatusInfo.setReadOnly(True)
        self.StatusInfo.setObjectName("StatusInfo")
        self.horizontalLayout_3.addWidget(self.StatusInfo)
        
        self.Startbtn = QtWidgets.QPushButton(self.BtnBox)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Startbtn.setFont(font)
        self.Startbtn.setObjectName("Startbtn")
        self.horizontalLayout_3.addWidget(self.Startbtn)
        
        self.Pausebtn = QtWidgets.QPushButton(self.BtnBox)
        self.Pausebtn.setEnabled(False)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.Pausebtn.setFont(font)
        self.Pausebtn.setObjectName("Pausebtn")
        self.horizontalLayout_3.addWidget(self.Pausebtn)
        
        self.Stopbtn = QtWidgets.QPushButton(self.BtnBox)
        self.Stopbtn.setEnabled(False)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.Stopbtn.setFont(font)
        self.Stopbtn.setObjectName("Stopbtn")
        self.horizontalLayout_3.addWidget(self.Stopbtn)
        
        self.PresetLabel = QtWidgets.QLabel(self.BtnBox)
        self.PresetLabel.setObjectName("PresetLabel")
        self.horizontalLayout_3.addWidget(self.PresetLabel)
        
        self.horizontalLayout_3.setStretch(1, 2)
        self.horizontalLayout_3.setStretch(2, 2)
        self.horizontalLayout_3.setStretch(3, 2)

        self.gridLayout.addWidget(self.BtnBox, 13, 0, 1, 1)
        
        self.gridLayout.setRowStretch(0, 1)
        self.gridLayout.setRowStretch(1, 2)
        self.gridLayout.setRowStretch(2, 6)
        self.gridLayout.setRowStretch(5, 1)
        self.verticalLayout_3.addWidget(self.Container)
        
        _translate = QtCore.QCoreApplication.translate
        PUBG.setWindowTitle(_translate("PUBG", "ROFLS"))
        self.Title.setText(_translate("PUBG", "ROFLS"))
        self.Name1Lable.setText(_translate("PUBG", "Название:"))
        self.Muzzle1Lable.setText(_translate("PUBG", "Дульный тормоз:"))
        self.Scope1Lable.setText(_translate("PUBG", "Прицел:"))
        self.Grip1Lable.setText(_translate("PUBG", "Рукоятка:"))
        self.Butt1Label.setText(_translate("PUBG", "Приклад:"))
        self.Name2Lable.setText(_translate("PUBG", "Название:"))
        self.Muzzle2Lable.setText(_translate("PUBG", "Дульный тормоз:"))
        self.Scope2Lable.setText(_translate("PUBG", "Прицел:"))
        self.Grip2Lable.setText(_translate("PUBG", "Рукоятка:"))
        self.Butt2Lable.setText(_translate("PUBG", "Приклад:"))
        self.InfoBoxs.setTitle(_translate("PUBG", "Вывод журнала"))
        self.Resolution.setTitle(_translate("PUBG", "Настройки разрешения"))
        self.ResolutionLabel.setText(_translate("PUBG", "Текущее разрешение:"))
        self.ResolutionSelect.setItemText(1, _translate("PUBG", "1728x1080"))
        self.ResolutionSelect.setItemText(2, _translate("PUBG", "2560x1080"))
        self.ResolutionSelect.setItemText(3, _translate("PUBG", "2560x1440"))
        self.ResolutionSelect.setItemText(4, _translate("PUBG", "2560x1600"))
        self.ResolutionSelect.setItemText(5, _translate("PUBG", "3440x1440"))
        self.ResolutionSelect.setItemText(6, _translate("PUBG", "3840x2160"))
        self.ResolutionBtn.setText(_translate("PUBG", "Сохранить настройки"))
        
        
        self.CtrlStatusLabel.setText(_translate("PUBG", "Статус приседания:"))
        self.CtrlStatus.setText(_translate("PUBG", "Не зажат"))
        self.ShiftStatusLabel.setText(_translate("PUBG", "Статус SHIFT:"))
        self.ShiftStatus.setText(_translate("PUBG", "Не зажат"))
        self.StatusInfo.setText(_translate("PUBG", "Не запущено..."))
        self.Startbtn.setText(_translate("PUBG", "Запустить"))
        self.Pausebtn.setText(_translate("PUBG", "Пауза"))
        self.Stopbtn.setText(_translate("PUBG", "Выход"))
        self.CrosshairBtn.setText(_translate("PUBG", "Включить прицел"))

        self.ScopeMode.hide()
        self.IsScope.hide()
        self.EquipBox.hide()
        self.PoseBox.hide()

    def update_preset_label(self, preset_name):
        self.PresetLabel.setText(f"Текущий пресет: {preset_name}")
        self.Info.append(f"Текущий пресет: {preset_name}\n")
        self.Info.append(f"Текущее снаряжение: {self.OneGuns.text()}\n")

    def Init_UI_LOG(self, info):
        self.Info.append(info + "\n")
        if "инициализации" in info.lower() or "завершена" in info.lower():
            current_equip = self.OneGuns.text()
            self.Info.append(f"Текущее снаряжение: {current_equip}\n")
            print(f"Текущее снаряжение: {current_equip}")

    def update_ctrl_status(self, pressed):
        if pressed:
            self.CtrlStatus.setText("Зажат")
        else:
            self.CtrlStatus.setText("Не зажат")

    def update_shift_status(self, pressed):
        if pressed:
            self.ShiftStatus.setText("Зажат")
        else:
            self.ShiftStatus.setText("Не зажат")

    def init_ui(self):
        self.setupUi(self)
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        self.Init_UI_LOG("Программа находится в стадии инициализации.....")
        self.Init_UI_Win()
        self.Init_UI_GunsData()
        self.ResolutionSelect.setCurrentText(PC.Monitor)
        
        self.Init_UI_Btn()
        self.Init_UI_LOG("Инициализация программы завершена.....")
        self.Info.append(f"Текущее снаряжение: {self.OneGuns.text()}\n")
