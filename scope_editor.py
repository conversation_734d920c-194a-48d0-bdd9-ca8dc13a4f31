from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QLabel, QComboBox, QDoubleSpinBox, QGroupBox, QTreeWidget,
                           QTreeWidgetItem, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import json

class ScopeEditor(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Scope Multiplier Editor")
        self.setMinimumSize(600, 800)
        self.parent = parent
        self.scope_multipliers = {
            'none': 1.0,
            'hongdian': 1.3,
            'quanxi': 1.0,
            '2x': 2.0,
            '3x': 3.0,
            '4x': 4.0,
            '6x': 6.0,
            '8x': 8.0,
            '15x': 15.0
        }
        self.setup_ui()
        self.load_scopes()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        tree_group = QGroupBox("Scope List")
        tree_layout = QVBoxLayout(tree_group)
        
        self.scope_tree = QTreeWidget()
        self.scope_tree.setHeaderLabels(["Scope", "Multiplier"])
        self.scope_tree.setFont(QFont('Segoe UI', 10))
        self.scope_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #242424;
                color: #b388ff;
                border: 1px solid #4a148c;
                border-radius: 4px;
            }
            QTreeWidget::item:selected {
                background-color: #4a148c;
            }
            QTreeWidget::item:hover {
                background-color: #6a1b9a;
            }
        """)
        tree_layout.addWidget(self.scope_tree)

        edit_group = QGroupBox("Editing")
        edit_layout = QVBoxLayout(edit_group)

        controls_layout = QHBoxLayout()

        scope_layout = QVBoxLayout()
        scope_label = QLabel("Scope:")
        self.scope_combo = QComboBox()
        self.scope_combo.addItems([
            "none", "hongdian", "quanxi",
            "2x", "3x", "4x", "6x", "8x", "15x"
        ])
        scope_layout.addWidget(scope_label)
        scope_layout.addWidget(self.scope_combo)

        value_layout = QVBoxLayout()
        value_label = QLabel("Multiplier:")
        self.value_spin = QDoubleSpinBox()
        self.value_spin.setRange(0.0, 15.0)
        self.value_spin.setDecimals(1)
        self.value_spin.setSingleStep(0.1)
        value_layout.addWidget(value_label)
        value_layout.addWidget(self.value_spin)
        
        controls_layout.addLayout(scope_layout)
        controls_layout.addLayout(value_layout)
        edit_layout.addLayout(controls_layout)

        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("Add/Update")
        self.add_btn.clicked.connect(self.update_scope)
        self.reset_btn = QPushButton("Reset")
        self.reset_btn.clicked.connect(self.reset_scope)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.reset_btn)
        edit_layout.addLayout(button_layout)

        layout.addWidget(tree_group, stretch=2)
        layout.addWidget(edit_group, stretch=1)

        dialog_buttons = QHBoxLayout()
        save_btn = QPushButton("Save All")
        save_btn.clicked.connect(self.save_scopes)
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        dialog_buttons.addWidget(save_btn)
        dialog_buttons.addWidget(cancel_btn)
        layout.addLayout(dialog_buttons)

        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
            }
            QGroupBox {
                background-color: #242424;
                border: 1px solid #4a148c;
                border-radius: 8px;
                color: #e1bee7;
                font-weight: bold;
                padding: 15px;
                margin-top: 10px;
            }
            QLabel {
                color: #e1bee7;
                font-size: 11px;
            }
            QPushButton {
                background-color: #4a148c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6a1b9a;
            }
            QComboBox, QDoubleSpinBox {
                background-color: #242424;
                color: #b388ff;
                border: 1px solid #4a148c;
                border-radius: 4px;
                padding: 5px;
            }
        """)

    def load_scopes(self):
        """Load current scope values"""
        try:
            # First check if we can get values from PC instance (runtime values)
            runtime_values_loaded = False
            
            # Try to access the PC instance from main.py
            try:
                import sys
                # Get PC instance from main module
                if 'PC' in sys.modules['__main__'].__dict__:
                    PC = sys.modules['__main__'].PC
                    if hasattr(PC, 'ScopeData') and PC.ScopeData:
                        print("Loading scope values from runtime PC instance")
                        
                        # Conversion map for both directions
                        conversion = {
                            '2bei': '2x',
                            '3bei': '3x',
                            '4bei': '4x',
                            '6bei': '6x',
                            '8bei': '8x',
                            '15bei': '15x'
                        }
                        
                        # Update scope multipliers from runtime values
                        for scope, value in PC.ScopeData.items():
                            if scope in conversion:
                                x_format = conversion[scope]
                                self.scope_multipliers[x_format] = float(value)
                                print(f"Loaded runtime scope: {scope} -> {x_format} = {value}")
                            elif scope in ['none', 'hongdian', 'quanxi', '2x', '3x', '4x', '6x', '8x', '15x']:
                                self.scope_multipliers[scope] = float(value)
                                print(f"Loaded runtime scope: {scope} = {value}")
                        
                        runtime_values_loaded = True
            except Exception as e:
                print(f"Error loading runtime scope values: {e}")
            
            # If runtime values weren't loaded, fall back to config file
            if not runtime_values_loaded:
                with open('./Config/config.json', 'r') as f:
                    config = json.load(f)
                    loaded_scopes = config.get('sensitivity', {})
                    if isinstance(loaded_scopes, dict):
                        # Conversion map for both directions
                        conversion = {
                            '2bei': '2x',
                            '3bei': '3x',
                            '4bei': '4x',
                            '6bei': '6x',
                            '8bei': '8x',
                            '15bei': '15x'
                        }
                        
                        # Update scope multipliers from loaded values
                        for scope, value in loaded_scopes.items():
                            # Convert bei format to x format for display
                            scope_name = conversion.get(scope, scope)
                            self.scope_multipliers[scope_name] = float(value)
                            print(f"Loaded scope from config: {scope} -> {scope_name} = {value}")

            # Update the UI
            self.update_scope_list()
            
            # Set the current value in the spinner
            current_scope = self.scope_combo.currentText()
            if current_scope in self.scope_multipliers:
                self.value_spin.setValue(self.scope_multipliers[current_scope])
                
        except FileNotFoundError:
            print("Config file not found, using default values")
        except json.JSONDecodeError:
            print("Invalid JSON in config file, using default values")
        except Exception as e:
            print(f"Error loading scope settings: {str(e)}")
            QMessageBox.warning(self, "Error", f"Failed to load settings: {str(e)}")
            
        # Make sure tree is populated even if loading fails
        self.update_scope_list()

    def update_scope_list(self):
        """Update the tree list"""
        self.scope_tree.clear()
        for scope, multiplier in sorted(self.scope_multipliers.items()):
            item = QTreeWidgetItem([scope, f"{float(multiplier):.1f}"])
            self.scope_tree.addTopLevelItem(item)

    def update_scope(self):
        """Update multiplier value"""
        try:
            scope = self.scope_combo.currentText()
            multiplier = self.value_spin.value()
            
            if not isinstance(self.scope_multipliers, dict):
                self.scope_multipliers = {}
                
            self.scope_multipliers[scope] = float(multiplier)
            self.update_scope_list()
            QMessageBox.information(self, "Success", f"Multiplier for {scope} updated: {multiplier:.1f}x")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to update multiplier: {str(e)}")

    def reset_scope(self):
        """Reset value to default"""
        scope = self.scope_combo.currentText()
        default_values = {
            'none': 1.0, 'hongdian': 1.3, 'quanxi': 1.0,
            '2x': 2.0, '3x': 3.0, '4x': 4.0,
            '6x': 6.0, '8x': 8.0, '15x': 15.0
        }
        if scope in default_values:
            self.value_spin.setValue(default_values[scope])
            self.update_scope()

    def save_scopes(self):
        """Save values with conversion to bei format"""
        try:
            converted_multipliers = {}
            conversion = {
                '2x': '2bei',
                '3x': '3bei',
                '4x': '4bei',
                '6x': '6bei',
                '8x': '8bei',
                '15x': '15bei'
            }
            
            # Convert from UI format (2x) to storage format (2bei)
            for scope, value in self.scope_multipliers.items():
                scope_name = conversion.get(scope, scope)
                converted_multipliers[scope_name] = float(value)
                print(f"Saving scope: {scope} -> {scope_name} = {value}")
            
            # Load existing config or create new one
            try:
                with open('./Config/config.json', 'r') as f:
                    config = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                config = {}
            
            # Update sensitivity section
            config['sensitivity'] = converted_multipliers

            # Save to file with pretty formatting
            with open('./Config/config.json', 'w') as f:
                json.dump(config, f, indent=4)

            # Update macro module with new values
            try:
                import macro
                # Update both formats for compatibility
                for scope, value in converted_multipliers.items():
                    macro.scope_multipliers[scope] = float(value)
                    # Also update the x format if it exists
                    x_format = scope.replace('bei', 'x') if 'bei' in scope else scope
                    if x_format != scope:
                        macro.scope_multipliers[x_format] = float(value)
                        
                # Force recalculation of recoil with new values
                macro.recalculate_recoil()
                print("Macro module updated with new scope values")
                
                # Also update PC instance if available
                try:
                    import sys
                    if 'PC' in sys.modules['__main__'].__dict__:
                        PC = sys.modules['__main__'].PC
                        if hasattr(PC, 'ScopeData'):
                            for scope, value in converted_multipliers.items():
                                PC.ScopeData[scope] = float(value)
                                # Also update the x format if it's a bei format
                                if 'bei' in scope:
                                    x_format = scope.replace('bei', 'x')
                                    PC.ScopeData[x_format] = float(value)
                            print("Updated PC instance with new scope values")
                except Exception as e:
                    print(f"Error updating PC instance: {e}")
                
            except Exception as e:
                print(f"Error updating macro module: {e}")
                
            QMessageBox.information(self, "Success", "Settings saved and applied")
            self.accept()
            
        except Exception as e:
            print(f"Error saving scope settings: {e}")
            QMessageBox.warning(self, "Error", f"Failed to save settings: {str(e)}")

    def reject(self):
        """Cancel changes"""
        if QMessageBox.question(self, "Confirmation", 
                              "Discard all changes?",
                              QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes:
            super().reject()
